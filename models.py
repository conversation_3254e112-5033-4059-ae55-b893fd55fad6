from typing import List, Optional
from datetime import datetime


class Customer:
    """كلاس يمثل عميل في النظام"""

    def __init__(self, customer_id: int = None, name: str = "", phone: str = "",
                 email: str = "", address: str = "", tax_number: str = "",
                 created_at: str = None):
        """تهيئة كائن العميل"""
        self.customer_id = customer_id
        self.name = name
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.created_at = created_at

    def validate(self) -> bool:
        """التحقق من صحة بيانات العميل"""
        if not self.name.strip():
            return False

        # التحقق من صحة البريد الإلكتروني إذا تم إدخاله
        if self.email and "@" not in self.email:
            return False

        return True

    def to_dict(self) -> dict:
        """تحويل كائن العميل إلى قاموس"""
        return {
            'customer_id': self.customer_id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'tax_number': self.tax_number,
            'created_at': self.created_at
        }

    @classmethod
    def from_dict(cls, data: dict):
        """إنشاء كائن عميل من قاموس"""
        return cls(
            customer_id=data.get('customer_id'),
            name=data.get('name', ''),
            phone=data.get('phone', ''),
            email=data.get('email', ''),
            address=data.get('address', ''),
            tax_number=data.get('tax_number', ''),
            created_at=data.get('created_at')
        )


class Item:
    """كلاس يمثل صنف في النظام"""
    
    def __init__(self, item_id: int, name: str, description: str, 
                 price: float, barcode: str, tax_rate: float = 0.0):
        """تهيئة كائن الصنف"""
        self.item_id = item_id
        self.name = name
        self.description = description
        self.price = price
        self.barcode = barcode
        self.tax_rate = tax_rate
    
    def calculate_tax(self, quantity: int) -> float:
        """حساب مبلغ الضريبة لكمية معينة من الصنف"""
        subtotal = self.price * quantity
        return subtotal * self.tax_rate
    
    def calculate_subtotal(self, quantity: int) -> float:
        """حساب الإجمالي الفرعي لكمية معينة من الصنف"""
        return self.price * quantity
    
    def calculate_total_with_tax(self, quantity: int) -> float:
        """حساب الإجمالي مع الضريبة لكمية معينة من الصنف"""
        subtotal = self.calculate_subtotal(quantity)
        tax = self.calculate_tax(quantity)
        return subtotal + tax
    
    def __str__(self) -> str:
        """تمثيل نصي للصنف"""
        return f"{self.name} - {self.price:.2f} ريال (باركود: {self.barcode})"
    
    def __repr__(self) -> str:
        """تمثيل تقني للصنف"""
        return f"Item(id={self.item_id}, name='{self.name}', price={self.price}, barcode='{self.barcode}')"


class InvoiceItem:
    """كلاس يمثل صنف داخل فاتورة معينة"""
    
    def __init__(self, invoice_item_id: int, invoice_id: int, item_id: int,
                 quantity: int, unit_price: float, item_tax_rate: float,
                 subtotal: float, tax_amount: float, name: str = "",
                 description: str = "", barcode: str = ""):
        """تهيئة كائن صنف الفاتورة"""
        self.invoice_item_id = invoice_item_id
        self.invoice_id = invoice_id
        self.item_id = item_id
        self.quantity = quantity
        self.unit_price = unit_price
        self.item_tax_rate = item_tax_rate
        self.subtotal = subtotal
        self.tax_amount = tax_amount
        # معلومات إضافية من جدول الأصناف
        self.name = name
        self.description = description
        self.barcode = barcode
    
    @classmethod
    def from_item(cls, item: Item, quantity: int, invoice_id: int = 0, 
                  invoice_item_id: int = 0) -> 'InvoiceItem':
        """إنشاء InvoiceItem من Item مع حساب الإجماليات"""
        subtotal = item.calculate_subtotal(quantity)
        tax_amount = item.calculate_tax(quantity)
        
        return cls(
            invoice_item_id=invoice_item_id,
            invoice_id=invoice_id,
            item_id=item.item_id,
            quantity=quantity,
            unit_price=item.price,
            item_tax_rate=item.tax_rate,
            subtotal=subtotal,
            tax_amount=tax_amount,
            name=item.name,
            description=item.description,
            barcode=item.barcode
        )
    
    def get_total_with_tax(self) -> float:
        """حساب الإجمالي مع الضريبة"""
        return self.subtotal + self.tax_amount
    
    def __str__(self) -> str:
        """تمثيل نصي لصنف الفاتورة"""
        return f"{self.name} x{self.quantity} = {self.subtotal:.2f} + ضريبة {self.tax_amount:.2f} = {self.get_total_with_tax():.2f} ريال"


class Invoice:
    """كلاس يمثل فاتورة عرض سعر"""
    
    def __init__(self, invoice_id: int = 0, invoice_date: str = "", 
                 customer_name: str = "", total_amount: float = 0.0, 
                 total_tax: float = 0.0):
        """تهيئة كائن الفاتورة"""
        self.invoice_id = invoice_id
        self.invoice_date = invoice_date if invoice_date else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.customer_name = customer_name
        self.total_amount = total_amount
        self.total_tax = total_tax
        self.items: List[InvoiceItem] = []
    
    def add_item(self, item: Item, quantity: int) -> bool:
        """إضافة صنف إلى الفاتورة"""
        if quantity <= 0:
            return False
        
        # التحقق من وجود الصنف مسبقاً في الفاتورة
        existing_item = self.find_item_by_id(item.item_id)
        if existing_item:
            # زيادة الكمية إذا كان الصنف موجود
            new_quantity = existing_item.quantity + quantity
            existing_item.quantity = new_quantity
            existing_item.subtotal = item.calculate_subtotal(new_quantity)
            existing_item.tax_amount = item.calculate_tax(new_quantity)
        else:
            # إضافة صنف جديد
            invoice_item = InvoiceItem.from_item(item, quantity, self.invoice_id)
            self.items.append(invoice_item)
        
        self.calculate_totals()
        return True
    
    def remove_item(self, item_id: int) -> bool:
        """إزالة صنف من الفاتورة"""
        for i, item in enumerate(self.items):
            if item.item_id == item_id:
                del self.items[i]
                self.calculate_totals()
                return True
        return False
    
    def update_item_quantity(self, item_id: int, new_quantity: int) -> bool:
        """تحديث كمية صنف في الفاتورة"""
        if new_quantity <= 0:
            return self.remove_item(item_id)
        
        item = self.find_item_by_id(item_id)
        if item:
            item.quantity = new_quantity
            item.subtotal = item.unit_price * new_quantity
            item.tax_amount = item.subtotal * item.item_tax_rate
            self.calculate_totals()
            return True
        return False
    
    def find_item_by_id(self, item_id: int) -> Optional[InvoiceItem]:
        """البحث عن صنف في الفاتورة بواسطة معرف الصنف"""
        for item in self.items:
            if item.item_id == item_id:
                return item
        return None
    
    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        subtotal = sum(item.subtotal for item in self.items)
        total_tax = sum(item.tax_amount for item in self.items)
        self.total_tax = total_tax
        self.total_amount = subtotal + total_tax
    
    def get_subtotal(self) -> float:
        """حساب الإجمالي الفرعي (بدون ضريبة)"""
        return sum(item.subtotal for item in self.items)
    
    def get_item_count(self) -> int:
        """عدد الأصناف في الفاتورة"""
        return len(self.items)
    
    def get_total_quantity(self) -> int:
        """إجمالي الكميات في الفاتورة"""
        return sum(item.quantity for item in self.items)
    
    def is_empty(self) -> bool:
        """التحقق من كون الفاتورة فارغة"""
        return len(self.items) == 0
    
    def clear(self):
        """مسح جميع أصناف الفاتورة"""
        self.items.clear()
        self.total_amount = 0.0
        self.total_tax = 0.0
    
    def __str__(self) -> str:
        """تمثيل نصي للفاتورة"""
        return f"فاتورة رقم {self.invoice_id} - {self.customer_name} - {self.total_amount:.2f} ريال"
    
    def __repr__(self) -> str:
        """تمثيل تقني للفاتورة"""
        return f"Invoice(id={self.invoice_id}, customer='{self.customer_name}', total={self.total_amount})"
