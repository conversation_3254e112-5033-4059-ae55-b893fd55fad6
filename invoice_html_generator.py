import os
import tempfile
import webbrowser
from datetime import datetime
from typing import List, Dict, Any
from models import Invoice, InvoiceItem


class InvoiceHTMLGenerator:
    """مولد HTML للفواتير مع تنسيق جميل ومرتب"""
    
    def __init__(self):
        """تهيئة مولد HTML"""
        self.company_name = "شركة الفواتير المتقدمة"
        self.company_address = "المملكة العربية السعودية"
        self.company_phone = "+966 50 123 4567"
        self.company_email = "<EMAIL>"
    
    def generate_invoice_html(self, invoice: Invoice, invoice_items: List[InvoiceItem] = None) -> str:
        """توليد HTML للفاتورة"""
        
        # استخدام أصناف الفاتورة إذا لم يتم تمرير قائمة منفصلة
        items_to_use = invoice_items if invoice_items else invoice.items
        
        html_content = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {invoice.invoice_id or 'جديدة'}</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="invoice-container">
        {self._generate_header(invoice)}
        {self._generate_customer_info(invoice)}
        {self._generate_items_table(items_to_use)}
        {self._generate_totals(invoice)}
        {self._generate_footer()}
    </div>
    
    <div class="print-controls">
        <button onclick="window.print()" class="print-btn">🖨️ طباعة الفاتورة</button>
        <button onclick="window.close()" class="close-btn">❌ إغلاق</button>
    </div>
    
    <script>
        {self._get_javascript()}
    </script>
</body>
</html>
"""
        return html_content
    
    def _get_css_styles(self) -> str:
        """الحصول على أنماط CSS"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .invoice-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .company-name {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }
        
        .invoice-title {
            font-size: 1.8em;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .company-info {
            font-size: 1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .detail-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .detail-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        
        .detail-value {
            color: #2c3e50;
        }
        
        .items-section {
            padding: 30px;
        }
        
        .items-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .items-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .items-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tbody tr:hover {
            background: #e3f2fd;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }
        
        .totals-section {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 30px;
        }
        
        .totals-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            align-items: start;
        }
        
        .totals-summary {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .total-row:last-child {
            border-bottom: none;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            margin-top: 10px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .invoice-footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .print-controls button {
            padding: 12px 25px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .print-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        
        .close-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }
        
        .close-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .print-controls {
                display: none;
            }
            
            .invoice-container {
                box-shadow: none;
                margin: 0;
            }
        }
        
        .no-items {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-size: 1.2em;
        }
        """
    
    def _generate_header(self, invoice: Invoice) -> str:
        """توليد رأس الفاتورة"""
        return f"""
        <div class="invoice-header">
            <div class="company-name">{self.company_name}</div>
            <div class="invoice-title">فاتورة عرض سعر</div>
            <div class="company-info">
                {self.company_address} | {self.company_phone} | {self.company_email}
            </div>
        </div>
        """
    
    def _generate_customer_info(self, invoice: Invoice) -> str:
        """توليد معلومات العميل والفاتورة"""
        invoice_date = invoice.invoice_date or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        invoice_id = invoice.invoice_id or "جديدة"
        customer_name = invoice.customer_name or "غير محدد"
        
        return f"""
        <div class="invoice-details">
            <div class="detail-section">
                <h3>📋 معلومات الفاتورة</h3>
                <div class="detail-item">
                    <span class="detail-label">رقم الفاتورة:</span>
                    <span class="detail-value">{invoice_id}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">تاريخ الإنشاء:</span>
                    <span class="detail-value">{invoice_date}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">حالة الفاتورة:</span>
                    <span class="detail-value">مسودة</span>
                </div>
            </div>
            
            <div class="detail-section">
                <h3>👤 معلومات العميل</h3>
                <div class="detail-item">
                    <span class="detail-label">اسم العميل:</span>
                    <span class="detail-value">{customer_name}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">تاريخ الطباعة:</span>
                    <span class="detail-value">{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</span>
                </div>
            </div>
        </div>
        """

    def _generate_items_table(self, items: List[InvoiceItem]) -> str:
        """توليد جدول الأصناف"""
        if not items:
            return """
            <div class="items-section">
                <h2 class="items-title">📦 أصناف الفاتورة</h2>
                <div class="no-items">
                    <p>لا توجد أصناف في هذه الفاتورة</p>
                </div>
            </div>
            """

        items_rows = ""
        for i, item in enumerate(items, 1):
            quantity_str = f"{item.quantity:g}"
            items_rows += f"""
            <tr>
                <td>{i}</td>
                <td>{item.name}</td>
                <td>{item.description or '-'}</td>
                <td>{quantity_str}</td>
                <td>{item.unit_price:.2f} ريال</td>
                <td>{item.item_tax_rate * 100:.1f}%</td>
                <td>{item.subtotal:.2f} ريال</td>
                <td>{item.tax_amount:.2f} ريال</td>
                <td><strong>{item.get_total_with_tax():.2f} ريال</strong></td>
            </tr>
            """

        return f"""
        <div class="items-section">
            <h2 class="items-title">📦 أصناف الفاتورة</h2>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الصنف</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>نسبة الضريبة</th>
                        <th>الإجمالي الفرعي</th>
                        <th>مبلغ الضريبة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    {items_rows}
                </tbody>
            </table>
        </div>
        """

    def _generate_totals(self, invoice: Invoice) -> str:
        """توليد قسم الإجماليات"""
        subtotal = invoice.get_subtotal()
        tax_total = invoice.total_tax
        total = invoice.total_amount
        items_count = len(invoice.items)
        total_quantity = sum(item.quantity for item in invoice.items)

        return f"""
        <div class="totals-section">
            <div class="totals-grid">
                <div class="detail-section">
                    <h3>📊 ملخص الفاتورة</h3>
                    <div class="detail-item">
                        <span class="detail-label">عدد الأصناف:</span>
                        <span class="detail-value">{items_count} صنف</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">إجمالي الكميات:</span>
                        <span class="detail-value">{total_quantity:g}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">متوسط سعر الصنف:</span>
                        <span class="detail-value">{(subtotal / items_count) if items_count > 0 else 0:.2f} ريال</span>
                    </div>
                </div>

                <div class="totals-summary">
                    <div class="total-row">
                        <span>الإجمالي الفرعي:</span>
                        <span>{subtotal:.2f} ريال</span>
                    </div>
                    <div class="total-row">
                        <span>إجمالي الضريبة:</span>
                        <span>{tax_total:.2f} ريال</span>
                    </div>
                    <div class="total-row">
                        <span>💰 الإجمالي الكلي:</span>
                        <span>{total:.2f} ريال</span>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_footer(self) -> str:
        """توليد تذييل الفاتورة"""
        return f"""
        <div class="invoice-footer">
            <p>شكراً لتعاملكم معنا | تم إنشاء هذه الفاتورة بواسطة نظام إدارة الفواتير المتقدم</p>
            <p>تاريخ الطباعة: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        </div>
        """

    def _get_javascript(self) -> str:
        """الحصول على كود JavaScript"""
        return """
        // تحسين تجربة الطباعة
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        });

        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للصفوف
            const rows = document.querySelectorAll('.items-table tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = 'none';
                });
            });
        });
        """

    def create_and_open_invoice(self, invoice: Invoice, invoice_items: List[InvoiceItem] = None) -> str:
        """إنشاء ملف HTML وفتحه في المتصفح"""
        try:
            # توليد محتوى HTML
            html_content = self.generate_invoice_html(invoice, invoice_items)

            # إنشاء ملف مؤقت
            temp_dir = tempfile.gettempdir()
            invoice_id = invoice.invoice_id or "new"
            filename = f"invoice_{invoice_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            file_path = os.path.join(temp_dir, filename)

            # كتابة الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح الملف في المتصفح
            webbrowser.open(f'file://{file_path}')

            return file_path

        except Exception as e:
            raise Exception(f"فشل في إنشاء ملف HTML: {str(e)}")

    def set_company_info(self, name: str = None, address: str = None,
                        phone: str = None, email: str = None):
        """تحديث معلومات الشركة"""
        if name:
            self.company_name = name
        if address:
            self.company_address = address
        if phone:
            self.company_phone = phone
        if email:
            self.company_email = email
