import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any
from database_manager import DatabaseManager
from main_gui import ModernStyle


class InvoicesViewWindow:
    """نافذة عرض الفواتير المحفوظة"""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        """تهيئة نافذة عرض الفواتير"""
        self.parent = parent
        self.db_manager = db_manager
        self.selected_invoice_id = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_invoices()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("الفواتير المحفوظة")
        self.window.geometry("1200x800")
        self.window.configure(bg=ModernStyle.BG_COLOR)
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(main_frame,
                               text="📋 الفواتير المحفوظة",
                               font=ModernStyle.FONT_TITLE,
                               foreground=ModernStyle.TEXT_PRIMARY,
                               background=ModernStyle.BG_COLOR)
        title_label.pack(pady=(0, 20))
        
        # إطار المحتوى
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار قائمة الفواتير
        self.create_invoices_list_frame(content_frame)
        
        # إطار تفاصيل الفاتورة
        self.create_invoice_details_frame(content_frame)
    
    def create_invoices_list_frame(self, parent):
        """إنشاء إطار قائمة الفواتير"""
        list_frame = ttk.LabelFrame(parent, text="قائمة الفواتير", padding=10)
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # إنشاء الجدول
        columns = ("رقم الفاتورة", "التاريخ", "العميل", "الإجمالي")
        self.invoices_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        self.invoices_tree.heading("رقم الفاتورة", text="رقم الفاتورة")
        self.invoices_tree.heading("التاريخ", text="التاريخ")
        self.invoices_tree.heading("العميل", text="اسم العميل")
        self.invoices_tree.heading("الإجمالي", text="الإجمالي (ريال)")
        
        # تعريف عرض الأعمدة
        self.invoices_tree.column("رقم الفاتورة", width=100, anchor=tk.CENTER)
        self.invoices_tree.column("التاريخ", width=150, anchor=tk.CENTER)
        self.invoices_tree.column("العميل", width=200, anchor=tk.E)
        self.invoices_tree.column("الإجمالي", width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.invoices_tree.bind("<<TreeviewSelect>>", self.on_invoice_select)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(list_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # زر تحديث
        refresh_btn = ttk.Button(buttons_frame, text="تحديث", 
                               command=self.load_invoices, style="Primary.TButton")
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر حذف
        delete_btn = ttk.Button(buttons_frame, text="حذف الفاتورة", 
                              command=self.delete_invoice, style="Accent.TButton")
        delete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر إغلاق
        close_btn = ttk.Button(buttons_frame, text="إغلاق", 
                             command=self.window.destroy)
        close_btn.pack(side=tk.RIGHT)
    
    def create_invoice_details_frame(self, parent):
        """إنشاء إطار تفاصيل الفاتورة"""
        details_frame = ttk.LabelFrame(parent, text="تفاصيل الفاتورة", padding=10)
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # معلومات الفاتورة
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # رقم الفاتورة
        ttk.Label(info_frame, text="رقم الفاتورة:", font=ModernStyle.FONT_MEDIUM).grid(row=0, column=0, sticky=tk.E, padx=(0, 10))
        self.invoice_id_label = ttk.Label(info_frame, text="-", font=ModernStyle.FONT_MEDIUM,
                                         foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.invoice_id_label.grid(row=0, column=1, sticky=tk.W)
        
        # التاريخ
        ttk.Label(info_frame, text="التاريخ:", font=ModernStyle.FONT_MEDIUM).grid(row=1, column=0, sticky=tk.E, padx=(0, 10), pady=5)
        self.invoice_date_label = ttk.Label(info_frame, text="-", font=ModernStyle.FONT_MEDIUM,
                                           foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.invoice_date_label.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # العميل
        ttk.Label(info_frame, text="العميل:", font=ModernStyle.FONT_MEDIUM).grid(row=2, column=0, sticky=tk.E, padx=(0, 10))
        self.customer_label = ttk.Label(info_frame, text="-", font=ModernStyle.FONT_MEDIUM,
                                       foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.customer_label.grid(row=2, column=1, sticky=tk.W)
        
        # جدول الأصناف
        items_label = ttk.Label(details_frame, text="أصناف الفاتورة:", font=ModernStyle.FONT_MEDIUM)
        items_label.pack(anchor=tk.W, pady=(10, 5))
        
        # إنشاء جدول الأصناف
        items_columns = ("الصنف", "الكمية", "السعر", "الإجمالي الفرعي", "الضريبة", "الإجمالي")
        self.items_tree = ttk.Treeview(details_frame, columns=items_columns, show="headings", height=8)
        
        # تعريف عناوين الأصناف
        for col in items_columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=100, anchor=tk.CENTER)
        
        # شريط تمرير للأصناف
        items_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # إطار الجدول
        items_table_frame = ttk.Frame(details_frame)
        items_table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الإجماليات
        totals_frame = ttk.LabelFrame(details_frame, text="الإجماليات", padding=10)
        totals_frame.pack(fill=tk.X, pady=(10, 0))
        
        # الإجمالي الفرعي
        ttk.Label(totals_frame, text="الإجمالي الفرعي:", font=ModernStyle.FONT_MEDIUM).grid(row=0, column=0, sticky=tk.E, padx=(0, 10))
        self.subtotal_label = ttk.Label(totals_frame, text="0.00 ريال", font=ModernStyle.FONT_MEDIUM,
                                       foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.subtotal_label.grid(row=0, column=1, sticky=tk.W)
        
        # إجمالي الضريبة
        ttk.Label(totals_frame, text="إجمالي الضريبة:", font=ModernStyle.FONT_MEDIUM).grid(row=1, column=0, sticky=tk.E, padx=(0, 10), pady=5)
        self.tax_label = ttk.Label(totals_frame, text="0.00 ريال", font=ModernStyle.FONT_MEDIUM,
                                  foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.tax_label.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # الإجمالي الكلي
        ttk.Label(totals_frame, text="الإجمالي الكلي:", font=ModernStyle.FONT_LARGE).grid(row=2, column=0, sticky=tk.E, padx=(0, 10))
        self.total_label = ttk.Label(totals_frame, text="0.00 ريال", font=ModernStyle.FONT_LARGE,
                                    foreground=ModernStyle.PRIMARY_COLOR, background=ModernStyle.BG_COLOR)
        self.total_label.grid(row=2, column=1, sticky=tk.W)
        
        # أزرار العمليات
        actions_frame = ttk.Frame(details_frame)
        actions_frame.pack(fill=tk.X, pady=(10, 0))
        
        # زر طباعة
        print_btn = ttk.Button(actions_frame, text="🖨️ طباعة الفاتورة", 
                             command=self.print_invoice, style="Primary.TButton")
        print_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تصدير
        export_btn = ttk.Button(actions_frame, text="📄 تصدير PDF", 
                              command=self.export_pdf, style="Secondary.TButton")
        export_btn.pack(side=tk.LEFT)
    
    def load_invoices(self):
        """تحميل الفواتير في الجدول"""
        # مسح الجدول
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)
        
        # جلب الفواتير
        invoices = self.db_manager.get_all_invoices()
        
        # إضافة الفواتير للجدول
        for invoice in invoices:
            # تنسيق التاريخ
            date_parts = invoice['invoice_date'].split(' ')
            formatted_date = date_parts[0] if date_parts else invoice['invoice_date']
            
            # اسم العميل
            customer_name = invoice.get('display_customer_name', invoice.get('customer_name', 'غير محدد'))
            
            self.invoices_tree.insert("", tk.END, values=(
                invoice['invoice_id'],
                formatted_date,
                customer_name,
                f"{invoice['total_amount']:.2f}"
            ))
    
    def on_invoice_select(self, event):
        """معالج تحديد فاتورة من الجدول"""
        selection = self.invoices_tree.selection()
        if selection:
            item_values = self.invoices_tree.item(selection[0])['values']
            self.selected_invoice_id = item_values[0]
            self.load_invoice_details()
    
    def load_invoice_details(self):
        """تحميل تفاصيل الفاتورة المحددة"""
        if not self.selected_invoice_id:
            return
        
        # جلب تفاصيل الفاتورة
        invoice_details = self.db_manager.get_invoice_details(self.selected_invoice_id)
        
        if not invoice_details:
            messagebox.showerror("خطأ", "فشل في جلب تفاصيل الفاتورة")
            return
        
        # تحديث معلومات الفاتورة
        self.invoice_id_label.config(text=str(invoice_details['invoice_id']))
        self.invoice_date_label.config(text=invoice_details['invoice_date'])
        # عرض اسم العميل مع معلومات إضافية إذا كان مرتبط بجدول العملاء
        customer_display = invoice_details.get('customer_full_name') or invoice_details.get('customer_name') or "غير محدد"
        self.customer_label.config(text=customer_display)
        
        # مسح جدول الأصناف
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
        
        # إضافة أصناف الفاتورة
        subtotal = 0
        for item in invoice_details['items']:
            total_with_tax = item['subtotal'] + item['tax_amount']
            subtotal += item['subtotal']
            
            self.items_tree.insert("", tk.END, values=(
                item['name'],
                item['quantity'],
                f"{item['unit_price']:.2f}",
                f"{item['subtotal']:.2f}",
                f"{item['tax_amount']:.2f}",
                f"{total_with_tax:.2f}"
            ))
        
        # تحديث الإجماليات
        self.subtotal_label.config(text=f"{subtotal:.2f} ريال")
        self.tax_label.config(text=f"{invoice_details['total_tax']:.2f} ريال")
        self.total_label.config(text=f"{invoice_details['total_amount']:.2f} ريال")

    def delete_invoice(self):
        """حذف الفاتورة المحددة"""
        if not self.selected_invoice_id:
            messagebox.showerror("خطأ", "يجب تحديد فاتورة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الفاتورة رقم {self.selected_invoice_id}؟\n"
                                   "لا يمكن التراجع عن هذا الإجراء.")

        if result:
            success = self.db_manager.delete_invoice(self.selected_invoice_id)

            if success:
                messagebox.showinfo("نجح", "تم حذف الفاتورة بنجاح")
                self.load_invoices()
                self.clear_details()
            else:
                messagebox.showerror("خطأ", "فشل في حذف الفاتورة")

    def clear_details(self):
        """مسح تفاصيل الفاتورة"""
        self.selected_invoice_id = None
        self.invoice_id_label.config(text="-")
        self.invoice_date_label.config(text="-")
        self.customer_label.config(text="-")

        # مسح جدول الأصناف
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # مسح الإجماليات
        self.subtotal_label.config(text="0.00 ريال")
        self.tax_label.config(text="0.00 ريال")
        self.total_label.config(text="0.00 ريال")

    def print_invoice(self):
        """طباعة الفاتورة المحددة في المتصفح بتنسيق جميل"""
        if not self.selected_invoice_id:
            messagebox.showerror("⚠️ تحذير", "يجب تحديد فاتورة للطباعة")
            return

        try:
            # جلب تفاصيل الفاتورة
            invoice_details = self.db_manager.get_invoice_details(self.selected_invoice_id)

            if not invoice_details:
                messagebox.showerror("❌ خطأ", "فشل في جلب تفاصيل الفاتورة من قاعدة البيانات")
                return

            # استيراد مولد HTML والنماذج
            from invoice_html_generator import InvoiceHTMLGenerator
            from models import Invoice, InvoiceItem

            # إنشاء كائن الفاتورة
            invoice = Invoice()
            invoice.invoice_id = invoice_details['invoice_id']
            invoice.invoice_date = invoice_details['invoice_date']
            invoice.customer_name = invoice_details['customer_name']
            invoice.total_amount = invoice_details['total_amount']
            invoice.total_tax = invoice_details['total_tax']

            # إنشاء قائمة أصناف الفاتورة
            invoice_items = []
            for item_data in invoice_details['items']:
                invoice_item = InvoiceItem(
                    name=item_data['name'],
                    description=item_data['description'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    item_tax_rate=item_data['tax_rate']
                )
                invoice_items.append(invoice_item)
                invoice.items.append(invoice_item)

            # إنشاء مولد HTML
            html_generator = InvoiceHTMLGenerator()

            # إنشاء وفتح الفاتورة في المتصفح
            file_path = html_generator.create_and_open_invoice(invoice, invoice_items)

            # رسالة نجاح
            messagebox.showinfo("✅ تم بنجاح",
                              f"تم فتح الفاتورة رقم {invoice.invoice_id} في المتصفح بنجاح!\n" +
                              f"📁 مسار الملف: {file_path}\n\n" +
                              f"💡 يمكنك الآن طباعة الفاتورة من المتصفح باستخدام Ctrl+P")

        except Exception as e:
            print(f"❌ خطأ في طباعة الفاتورة: {str(e)}")
            messagebox.showerror("❌ خطأ في الطباعة",
                               f"فشل في فتح الفاتورة في المتصفح:\n{str(e)}\n\n" +
                               f"💡 تأكد من وجود متصفح ويب مثبت على النظام")

            # العودة للطريقة القديمة في حالة الفشل
            self.show_print_preview(invoice_details)

    def show_print_preview(self, invoice_details):
        """عرض معاينة الطباعة"""
        preview_window = tk.Toplevel(self.window)
        preview_window.title(f"معاينة طباعة الفاتورة رقم {invoice_details['invoice_id']}")
        preview_window.geometry("700x900")
        preview_window.configure(bg="white")

        # إطار المحتوى مع شريط تمرير
        canvas = tk.Canvas(preview_window, bg="white")
        scrollbar = ttk.Scrollbar(preview_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط الكانفاس وشريط التمرير
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")

        # محتوى الفاتورة
        content_frame = ttk.Frame(scrollable_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان الفاتورة
        title_label = tk.Label(content_frame, text="فاتورة عرض سعر",
                              font=("Arial", 20, "bold"), bg="white")
        title_label.pack(pady=(0, 20))

        # معلومات الشركة (يمكن تخصيصها)
        company_info = """
شركة المثال للتجارة
العنوان: الرياض، المملكة العربية السعودية
الهاتف: +966 11 123 4567
البريد الإلكتروني: <EMAIL>
        """

        company_label = tk.Label(content_frame, text=company_info,
                                font=("Arial", 10), bg="white", justify=tk.CENTER)
        company_label.pack(pady=(0, 30))

        # خط فاصل
        separator1 = tk.Frame(content_frame, height=2, bg="black")
        separator1.pack(fill=tk.X, pady=(0, 20))

        # معلومات الفاتورة
        info_frame = tk.Frame(content_frame, bg="white")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # الجانب الأيسر - معلومات الفاتورة
        left_info = tk.Frame(info_frame, bg="white")
        left_info.pack(side=tk.LEFT, anchor=tk.NW)

        invoice_info = f"""رقم الفاتورة: {invoice_details['invoice_id']}
التاريخ: {invoice_details['invoice_date']}
        """

        tk.Label(left_info, text=invoice_info, font=("Arial", 12),
                bg="white", justify=tk.LEFT).pack(anchor=tk.W)

        # الجانب الأيمن - معلومات العميل
        right_info = tk.Frame(info_frame, bg="white")
        right_info.pack(side=tk.RIGHT, anchor=tk.NE)

        # معلومات العميل مع التفاصيل الإضافية إذا كانت متوفرة
        customer_name = invoice_details.get('customer_full_name') or invoice_details.get('customer_name') or 'غير محدد'
        customer_phone = invoice_details.get('phone', '')
        customer_address = invoice_details.get('address', '')

        customer_info = f"العميل: {customer_name}"
        if customer_phone:
            customer_info += f"\nالهاتف: {customer_phone}"
        if customer_address:
            customer_info += f"\nالعنوان: {customer_address}"

        tk.Label(right_info, text=customer_info, font=("Arial", 12),
                bg="white", justify=tk.RIGHT).pack(anchor=tk.E)

        # خط فاصل
        separator2 = tk.Frame(content_frame, height=1, bg="gray")
        separator2.pack(fill=tk.X, pady=20)

        # جدول الأصناف
        table_frame = tk.Frame(content_frame, bg="white")
        table_frame.pack(fill=tk.X, pady=(0, 20))

        # عنوان الجدول
        headers = ["الصنف", "الكمية", "السعر", "الإجمالي الفرعي", "الضريبة", "الإجمالي"]
        header_frame = tk.Frame(table_frame, bg="lightgray")
        header_frame.pack(fill=tk.X)

        for i, header in enumerate(headers):
            tk.Label(header_frame, text=header, font=("Arial", 10, "bold"),
                    bg="lightgray", relief=tk.RIDGE, borderwidth=1).grid(
                    row=0, column=i, sticky="ew", padx=1, pady=1)

        # تكوين الأعمدة
        for i in range(len(headers)):
            header_frame.grid_columnconfigure(i, weight=1)

        # صفوف البيانات
        for row_idx, item in enumerate(invoice_details['items'], 1):
            row_frame = tk.Frame(table_frame, bg="white")
            row_frame.pack(fill=tk.X)

            total_with_tax = item['subtotal'] + item['tax_amount']
            values = [
                item['name'],
                str(item['quantity']),
                f"{item['unit_price']:.2f}",
                f"{item['subtotal']:.2f}",
                f"{item['tax_amount']:.2f}",
                f"{total_with_tax:.2f}"
            ]

            for i, value in enumerate(values):
                tk.Label(row_frame, text=value, font=("Arial", 10),
                        bg="white", relief=tk.RIDGE, borderwidth=1).grid(
                        row=0, column=i, sticky="ew", padx=1, pady=1)

            # تكوين الأعمدة
            for i in range(len(values)):
                row_frame.grid_columnconfigure(i, weight=1)

        # خط فاصل
        separator3 = tk.Frame(content_frame, height=1, bg="gray")
        separator3.pack(fill=tk.X, pady=20)

        # الإجماليات
        totals_frame = tk.Frame(content_frame, bg="white")
        totals_frame.pack(anchor=tk.E, pady=(0, 20))

        subtotal = sum(item['subtotal'] for item in invoice_details['items'])

        totals_text = f"""الإجمالي الفرعي: {subtotal:.2f} ريال
إجمالي الضريبة: {invoice_details['total_tax']:.2f} ريال
الإجمالي الكلي: {invoice_details['total_amount']:.2f} ريال"""

        tk.Label(totals_frame, text=totals_text, font=("Arial", 12, "bold"),
                bg="white", justify=tk.RIGHT).pack(anchor=tk.E)

        # أزرار الطباعة
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(side=tk.BOTTOM, pady=20)

        print_btn = ttk.Button(buttons_frame, text="طباعة",
                             command=lambda: self.do_print(preview_window, invoice_details))
        print_btn.pack(side=tk.LEFT, padx=(0, 10))

        close_btn = ttk.Button(buttons_frame, text="إغلاق",
                             command=preview_window.destroy)
        close_btn.pack(side=tk.LEFT)

    def do_print(self, preview_window, invoice_details):
        """تنفيذ الطباعة الفعلية"""
        try:
            messagebox.showinfo("طباعة", f"تم إرسال الفاتورة رقم {invoice_details['invoice_id']} للطباعة")
            preview_window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"فشل في الطباعة: {str(e)}")

    def export_pdf(self):
        """تصدير الفاتورة إلى PDF"""
        if not self.selected_invoice_id:
            messagebox.showerror("خطأ", "يجب تحديد فاتورة للتصدير")
            return

        # هذه الوظيفة يمكن تطويرها لاحقاً لتصدير PDF فعلي
        messagebox.showinfo("تصدير PDF",
                           f"سيتم تصدير الفاتورة رقم {self.selected_invoice_id} إلى PDF\n"
                           "(هذه الميزة قيد التطوير)")
