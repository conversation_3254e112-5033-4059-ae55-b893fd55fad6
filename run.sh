#!/bin/bash

# نظام إدارة الفواتير وعروض الأسعار
# Invoice and Quotation Management System

# تعيين ترميز UTF-8
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

echo ""
echo "================================================"
echo "       نظام إدارة الفواتير وعروض الأسعار"
echo "       Invoice and Quotation Management System"
echo "================================================"
echo ""

echo "🚀 بدء تشغيل التطبيق..."
echo "🚀 Starting application..."
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ خطأ: Python غير مثبت على النظام"
    echo "❌ Error: Python is not installed"
    echo ""
    echo "يرجى تثبيت Python من: https://python.org"
    echo "Please install Python from: https://python.org"
    echo ""
    echo "على Ubuntu/Debian:"
    echo "sudo apt update && sudo apt install python3 python3-tk"
    echo ""
    echo "على CentOS/RHEL:"
    echo "sudo yum install python3 python3-tkinter"
    echo ""
    echo "على macOS:"
    echo "brew install python-tk"
    exit 1
fi

# تحديد أمر Python المناسب
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
fi

# عرض إصدار Python
echo "✅ تم العثور على Python:"
echo "✅ Python found:"
$PYTHON_CMD --version

echo ""
echo "📦 تشغيل التطبيق..."
echo "📦 Running application..."
echo ""

# تشغيل التطبيق
$PYTHON_CMD main.py

# التحقق من حالة الخروج
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ تم إغلاق التطبيق بنجاح"
    echo "✅ Application closed successfully"
else
    echo ""
    echo "❌ حدث خطأ أثناء تشغيل التطبيق"
    echo "❌ An error occurred while running the application"
    echo ""
    echo "للحصول على المساعدة، يرجى مراجعة ملف README.md"
    echo "For help, please check README.md file"
    echo ""
    echo "تشخيص المشاكل الشائعة:"
    echo "Common troubleshooting:"
    echo ""
    echo "1. تأكد من تثبيت tkinter:"
    echo "   Make sure tkinter is installed:"
    echo "   sudo apt install python3-tk  # Ubuntu/Debian"
    echo "   sudo yum install tkinter      # CentOS/RHEL"
    echo ""
    echo "2. تأكد من الصلاحيات:"
    echo "   Check permissions:"
    echo "   chmod +x run.sh"
    echo ""
    exit 1
fi

echo ""
read -p "اضغط Enter للمتابعة... Press Enter to continue..."
