#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الفواتير وعروض الأسعار
Invoice and Quotation Management System

تطبيق سطح مكتب شامل لإدارة الفواتير وعروض الأسعار
مع دعم الباركود والضرائب والطباعة

المطور: نظام الذكاء الاصطناعي
التاريخ: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from database_manager import DatabaseManager
    from main_gui import MainWindow
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)


class InvoiceSystemApp:
    """الفئة الرئيسية لتطبيق نظام الفواتير"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.db_manager = None
        self.main_app = None
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            error_msg = f"فشل في تهيئة قاعدة البيانات: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
            return False
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية"""
        try:
            # التحقق من وجود بيانات
            items = self.db_manager.get_all_items()
            if len(items) > 0:
                print("📦 البيانات موجودة بالفعل")
                return
            
            print("📝 إنشاء بيانات تجريبية...")
            
            # إضافة أصناف تجريبية
            sample_items = [
                {
                    "name": "لابتوب ديل",
                    "description": "لابتوب ديل انسبايرون 15 - معالج i5 - ذاكرة 8GB",
                    "price": 2500.00,
                    "barcode": "1234567890123",
                    "tax_rate": 15.0
                },
                {
                    "name": "ماوس لاسلكي",
                    "description": "ماوس لاسلكي لوجيتك - دقة عالية",
                    "price": 150.00,
                    "barcode": "2345678901234",
                    "tax_rate": 15.0
                },
                {
                    "name": "لوحة مفاتيح ميكانيكية",
                    "description": "لوحة مفاتيح ميكانيكية RGB - مفاتيح زرقاء",
                    "price": 350.00,
                    "barcode": "3456789012345",
                    "tax_rate": 15.0
                },
                {
                    "name": "شاشة LED 24 بوصة",
                    "description": "شاشة LED 24 بوصة - دقة Full HD",
                    "price": 800.00,
                    "barcode": "4567890123456",
                    "tax_rate": 15.0
                },
                {
                    "name": "طابعة ليزر",
                    "description": "طابعة ليزر أبيض وأسود - سرعة عالية",
                    "price": 1200.00,
                    "barcode": "5678901234567",
                    "tax_rate": 15.0
                },
                {
                    "name": "كابل HDMI",
                    "description": "كابل HDMI عالي الجودة - 2 متر",
                    "price": 45.00,
                    "barcode": "6789012345678",
                    "tax_rate": 15.0
                },
                {
                    "name": "سماعات بلوتوث",
                    "description": "سماعات بلوتوث لاسلكية - إلغاء الضوضاء",
                    "price": 450.00,
                    "barcode": "7890123456789",
                    "tax_rate": 15.0
                },
                {
                    "name": "هارد ديسك خارجي 1TB",
                    "description": "هارد ديسك خارجي محمول 1TB - USB 3.0",
                    "price": 300.00,
                    "barcode": "8901234567890",
                    "tax_rate": 15.0
                }
            ]
            
            # إضافة الأصناف
            for item in sample_items:
                success = self.db_manager.add_item(
                    name=item["name"],
                    description=item["description"],
                    price=item["price"],
                    barcode=item["barcode"],
                    tax_rate=item["tax_rate"]
                )
                if success:
                    print(f"✅ تم إضافة الصنف: {item['name']}")
                else:
                    print(f"❌ فشل في إضافة الصنف: {item['name']}")
            
            print("✅ تم إنشاء البيانات التجريبية بنجاح")
            
        except Exception as e:
            error_msg = f"فشل في إنشاء البيانات التجريبية: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)
    
    def initialize_gui(self):
        """تهيئة واجهة المستخدم"""
        try:
            self.main_app = MainWindow(self.db_manager)
            print("✅ تم تهيئة واجهة المستخدم بنجاح")
            return True
        except Exception as e:
            error_msg = f"فشل في تهيئة واجهة المستخدم: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في الواجهة", error_msg)
            return False
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            print("🚀 بدء تشغيل نظام إدارة الفواتير...")
            print("=" * 50)
            
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return False
            
            # إنشاء بيانات تجريبية
            self.create_sample_data()
            
            # تهيئة واجهة المستخدم
            if not self.initialize_gui():
                return False
            
            print("✅ تم تشغيل التطبيق بنجاح")
            print("=" * 50)
            
            # تشغيل الحلقة الرئيسية
            self.main_app.run()
            
            return True
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
            return True
            
        except Exception as e:
            error_msg = f"خطأ غير متوقع في التطبيق: {str(e)}"
            print(f"❌ {error_msg}")
            print("تفاصيل الخطأ:")
            traceback.print_exc()
            
            # عرض رسالة خطأ للمستخدم
            try:
                messagebox.showerror("خطأ في التطبيق", 
                                   f"{error_msg}\n\nيرجى إعادة تشغيل التطبيق.")
            except:
                pass  # في حالة فشل عرض الرسالة
            
            return False
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.db_manager:
                # يمكن إضافة عمليات تنظيف قاعدة البيانات هنا
                pass
            print("✅ تم تنظيف الموارد بنجاح")
        except Exception as e:
            print(f"⚠️ تحذير: فشل في تنظيف الموارد: {e}")


def main():
    """الدالة الرئيسية"""
    app = None
    try:
        # إنشاء التطبيق
        app = InvoiceSystemApp()
        
        # تشغيل التطبيق
        success = app.run()
        
        # تنظيف الموارد
        if app:
            app.cleanup()
        
        # إنهاء البرنامج
        if success:
            print("👋 تم إغلاق التطبيق بنجاح")
            sys.exit(0)
        else:
            print("❌ تم إغلاق التطبيق مع أخطاء")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ خطأ فادح في التطبيق: {e}")
        traceback.print_exc()
        
        # تنظيف الموارد في حالة الخطأ
        if app:
            try:
                app.cleanup()
            except:
                pass
        
        sys.exit(1)


if __name__ == "__main__":
    # تعيين ترميز UTF-8 للطباعة
    if sys.stdout.encoding != 'utf-8':
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    
    # تشغيل التطبيق
    main()
