# نظام إدارة الفواتير وعروض الأسعار
# Invoice and Quotation Management System
# متطلبات المشروع - Project Requirements

# المكتبات الأساسية المدمجة مع Python (لا تحتاج تثبيت)
# Built-in libraries (no installation needed):
# - tkinter (GUI framework)
# - sqlite3 (database)
# - datetime (date/time handling)
# - typing (type hints)
# - os (operating system interface)
# - sys (system-specific parameters)
# - traceback (error handling)

# المكتبات الإضافية الاختيارية
# Optional additional libraries:

# Pillow - لدعم الصور المحسن (اختياري)
# Pillow - Enhanced image support (optional)
Pillow>=8.0.0

# reportlab - لتصدير PDF (للتطوير المستقبلي)
# reportlab - PDF export (for future development)
# reportlab>=3.6.0

# openpyxl - لتصدير Excel (للتطوير المستقبلي)
# openpyxl - Excel export (for future development)  
# openpyxl>=3.0.0

# matplotlib - للرسوم البيانية (للتطوير المستقبلي)
# matplotlib - Charts and graphs (for future development)
# matplotlib>=3.5.0

# ملاحظات التثبيت:
# Installation notes:
#
# 1. للتثبيت الأساسي (يعمل التطبيق بدونها):
#    For basic installation (app works without these):
#    pip install -r requirements.txt
#
# 2. للتثبيت الكامل مع جميع الميزات المستقبلية:
#    For full installation with all future features:
#    قم بإلغاء التعليق عن المكتبات المطلوبة أعلاه
#    Uncomment the required libraries above
#
# 3. التحقق من الإصدارات:
#    Check versions:
#    pip list
#
# 4. تحديث المكتبات:
#    Update libraries:
#    pip install --upgrade -r requirements.txt
