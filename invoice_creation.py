import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Optional
from database_manager import DatabaseManager
from models import Item, Invoice, InvoiceItem
from main_gui import ModernStyle


class InvoiceCreationWindow:
    """نافذة إنشاء فاتورة جديدة"""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        """تهيئة نافذة إنشاء الفاتورة"""
        self.parent = parent
        self.db_manager = db_manager
        self.current_invoice = Invoice()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("📄 إنشاء فاتورة عرض سعر - شاشة كاملة")

        # فتح النافذة بالشاشة الكاملة
        self.window.state('zoomed')  # للويندوز
        # للأنظمة الأخرى
        try:
            self.window.attributes('-zoomed', True)
        except:
            # إذا فشل، استخدم الحد الأقصى للشاشة
            screen_width = self.window.winfo_screenwidth()
            screen_height = self.window.winfo_screenheight()
            self.window.geometry(f"{screen_width}x{screen_height}+0+0")

        self.window.configure(bg=ModernStyle.BG_COLOR)
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي مع تخطيط محسن للشاشة الكاملة
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # إطار علوي للعنوان ومعلومات الفاتورة
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 15))

        # إطار وسطي للمحتوى الرئيسي (إضافة الأصناف والجدول)
        middle_frame = ttk.Frame(main_frame)
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # إطار سفلي للإجماليات والأزرار
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)
        
        # العنوان في الإطار العلوي
        title_label = ttk.Label(top_frame,
                               text="📄 إنشاء فاتورة عرض سعر جديدة",
                               font=ModernStyle.FONT_TITLE,
                               foreground=ModernStyle.TEXT_PRIMARY,
                               background=ModernStyle.BG_COLOR)
        title_label.pack(side=tk.LEFT)

        # معلومات الفاتورة في الإطار العلوي
        self.create_invoice_info_frame(top_frame)

        # الأزرار الرئيسية في الإطار العلوي
        self.create_main_buttons_frame(top_frame)

        # تقسيم الإطار الوسطي إلى عمودين
        left_middle_frame = ttk.Frame(middle_frame)
        left_middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_middle_frame = ttk.Frame(middle_frame)
        right_middle_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # إطار إضافة الأصناف في العمود الأيسر
        self.create_add_items_frame(left_middle_frame)

        # إطار جدول الأصناف في العمود الأيمن
        self.create_items_table_frame(right_middle_frame)

        # الإجماليات والأزرار في الإطار السفلي
        self.create_totals_frame(bottom_frame)
        self.create_buttons_frame(bottom_frame)
    
    def create_invoice_info_frame(self, parent):
        """إنشاء إطار معلومات الفاتورة"""
        info_frame = ttk.LabelFrame(parent, text="📋 معلومات الفاتورة", padding=15)
        info_frame.pack(side=tk.RIGHT, padx=(20, 0))

        # العميل
        customer_frame = ttk.Frame(info_frame)
        customer_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(customer_frame, text="العميل:").pack(side=tk.LEFT)

        # قائمة منسدلة للعملاء
        self.customer_var = tk.StringVar()
        self.customer_combo = ttk.Combobox(customer_frame, textvariable=self.customer_var,
                                         width=40, state="readonly")
        self.customer_combo.pack(side=tk.LEFT, padx=(10, 10))
        self.customer_combo.bind('<<ComboboxSelected>>', self.on_customer_select)

        # زر إضافة عميل جديد
        add_customer_btn = ttk.Button(customer_frame, text="عميل جديد",
                                    command=self.add_new_customer)
        add_customer_btn.pack(side=tk.LEFT, padx=(5, 20))

        # أو إدخال اسم مباشر
        manual_frame = ttk.Frame(info_frame)
        manual_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(manual_frame, text="أو أدخل اسم العميل:").pack(side=tk.LEFT)

        self.customer_name_var = tk.StringVar()
        customer_entry = ttk.Entry(manual_frame, textvariable=self.customer_name_var, width=30)
        customer_entry.pack(side=tk.LEFT, padx=(10, 20))

        # تحميل العملاء
        self.load_customers_combo()

        # تاريخ الفاتورة
        date_frame = ttk.Frame(info_frame)
        date_frame.pack(fill=tk.X)

        ttk.Label(date_frame, text="التاريخ:").pack(side=tk.LEFT)

        date_label = ttk.Label(date_frame, text=self.current_invoice.invoice_date,
                              foreground=ModernStyle.TEXT_SECONDARY,
                              background=ModernStyle.BG_COLOR)
        date_label.pack(side=tk.LEFT, padx=(10, 0))

    def create_main_buttons_frame(self, parent):
        """إنشاء إطار الأزرار الرئيسية في أعلى الشاشة"""
        buttons_frame = ttk.LabelFrame(parent, text="🎛️ العمليات الرئيسية", padding=15)
        buttons_frame.pack(fill=tk.X, pady=(15, 0))

        # إطار الأزرار
        main_buttons_container = ttk.Frame(buttons_frame)
        main_buttons_container.pack(fill=tk.X)

        # الأزرار الأساسية (يسار)
        left_buttons = ttk.Frame(main_buttons_container)
        left_buttons.pack(side=tk.LEFT)

        # زر حفظ الفاتورة
        save_btn = ttk.Button(left_buttons, text="💾 حفظ الفاتورة (Ctrl+S)",
                            command=self.save_invoice, style="Primary.TButton",
                            width=22)
        save_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر طباعة الفاتورة
        print_btn = ttk.Button(left_buttons, text="🖨️ طباعة الفاتورة (Ctrl+P)",
                             command=self.print_invoice, style="Secondary.TButton",
                             width=22)
        print_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر معاينة الفاتورة
        preview_btn = ttk.Button(left_buttons, text="👁️ معاينة الفاتورة (F5)",
                               command=self.preview_invoice, style="Secondary.TButton",
                               width=22)
        preview_btn.pack(side=tk.LEFT, padx=(0, 15))

        # زر تصدير إلى Excel
        export_btn = ttk.Button(left_buttons, text="📊 تصدير Excel (Ctrl+E)",
                              command=self.export_to_excel, style="Secondary.TButton",
                              width=20)
        export_btn.pack(side=tk.LEFT, padx=(0, 15))

        # الأزرار الثانوية (يمين)
        right_buttons = ttk.Frame(main_buttons_container)
        right_buttons.pack(side=tk.RIGHT)

        # زر فاتورة جديدة
        new_btn = ttk.Button(right_buttons, text="📄 فاتورة جديدة",
                           command=self.new_invoice, style="Secondary.TButton",
                           width=15)
        new_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # زر مسح الفاتورة
        clear_btn = ttk.Button(right_buttons, text="🗑️ مسح الفاتورة",
                             command=self.clear_invoice, style="Accent.TButton",
                             width=15)
        clear_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # زر إغلاق
        close_btn = ttk.Button(right_buttons, text="❌ إغلاق",
                             command=self.window.destroy, style="Accent.TButton",
                             width=12)
        close_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # إضافة اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.window.bind('<Control-s>', lambda e: self.save_invoice())
        self.window.bind('<Control-p>', lambda e: self.print_invoice())
        self.window.bind('<F5>', lambda e: self.preview_invoice())
        self.window.bind('<Control-e>', lambda e: self.export_to_excel())
        self.window.bind('<Control-n>', lambda e: self.new_invoice())
        self.window.bind('<Delete>', lambda e: self.remove_selected_item())
        self.window.bind('<Escape>', lambda e: self.window.destroy())

    def create_add_items_frame(self, parent):
        """إنشاء إطار إضافة الأصناف"""
        add_frame = ttk.LabelFrame(parent, text="🛒 إضافة الأصناف", padding=20)
        add_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # الصف الأول - البحث بالباركود
        barcode_frame = ttk.Frame(add_frame)
        barcode_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(barcode_frame, text="🔍 البحث بالباركود:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)

        self.barcode_var = tk.StringVar()
        barcode_entry = ttk.Entry(barcode_frame, textvariable=self.barcode_var, width=25,
                                 font=ModernStyle.FONT_MEDIUM)
        barcode_entry.pack(side=tk.LEFT, padx=(10, 0))
        barcode_entry.bind('<Return>', self.search_by_barcode)

        search_btn = ttk.Button(barcode_frame, text="🔍 بحث",
                              command=self.search_by_barcode, style="Primary.TButton")
        search_btn.pack(side=tk.LEFT, padx=(10, 0))

        # الصف الثاني - البحث بالاسم
        name_frame = ttk.Frame(add_frame)
        name_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(name_frame, text="📋 أو اختر من القائمة:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)

        self.items_combo_var = tk.StringVar()
        self.items_combo = ttk.Combobox(name_frame, textvariable=self.items_combo_var,
                                       width=50, state="readonly", font=ModernStyle.FONT_MEDIUM)
        self.items_combo.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        self.items_combo.bind('<<ComboboxSelected>>', self.on_item_select)

        # تحميل الأصناف في القائمة المنسدلة
        self.load_items_combo()

        # إطار عرض تفاصيل الصنف المحدد
        self.create_item_details_frame(add_frame)

        # إطار تحكم في تفاصيل الإضافة
        self.create_add_controls_frame(add_frame)

        # متغيرات العميل
        self.selected_customer_id = None

    def create_item_details_frame(self, parent):
        """إنشاء إطار عرض تفاصيل الصنف المحدد"""
        details_frame = ttk.LabelFrame(parent, text="📋 تفاصيل الصنف المحدد", padding=15)
        details_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

        # إطار المعلومات في شكل بطاقة
        info_card = ttk.Frame(details_frame, relief="solid", borderwidth=1)
        info_card.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # الصف الأول - اسم الصنف والباركود
        row1_frame = ttk.Frame(info_card)
        row1_frame.pack(fill=tk.X, padx=15, pady=(15, 5))

        # اسم الصنف
        ttk.Label(row1_frame, text="📦 اسم الصنف:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.selected_item_name = ttk.Label(row1_frame, text="لم يتم اختيار صنف",
                                          foreground=ModernStyle.TEXT_SECONDARY,
                                          font=ModernStyle.FONT_MEDIUM)
        self.selected_item_name.pack(side=tk.LEFT, padx=(10, 0))

        # الباركود
        self.selected_item_barcode = ttk.Label(row1_frame, text="",
                                             foreground=ModernStyle.PRIMARY_COLOR,
                                             font=("Arial", 10, "bold"))
        self.selected_item_barcode.pack(side=tk.RIGHT)

        # الصف الثاني - الوصف
        row2_frame = ttk.Frame(info_card)
        row2_frame.pack(fill=tk.X, padx=15, pady=5)

        ttk.Label(row2_frame, text="📝 الوصف:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.selected_item_desc = ttk.Label(row2_frame, text="-",
                                          foreground=ModernStyle.TEXT_SECONDARY,
                                          font=ModernStyle.FONT_MEDIUM)
        self.selected_item_desc.pack(side=tk.LEFT, padx=(10, 0))

        # الصف الثالث - السعر والضريبة
        row3_frame = ttk.Frame(info_card)
        row3_frame.pack(fill=tk.X, padx=15, pady=5)

        # السعر
        price_frame = ttk.Frame(row3_frame)
        price_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(price_frame, text="💰 السعر:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.selected_item_price = ttk.Label(price_frame, text="-",
                                           foreground=ModernStyle.PRIMARY_COLOR,
                                           font=("Arial", 12, "bold"))
        self.selected_item_price.pack(side=tk.LEFT, padx=(10, 0))

        # الضريبة
        tax_frame = ttk.Frame(row3_frame)
        tax_frame.pack(side=tk.RIGHT)

        ttk.Label(tax_frame, text="🧾 الضريبة:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.selected_item_tax = ttk.Label(tax_frame, text="-",
                                         foreground=ModernStyle.TEXT_SECONDARY,
                                         font=ModernStyle.FONT_MEDIUM)
        self.selected_item_tax.pack(side=tk.LEFT, padx=(10, 0))

        # الصف الرابع - الكمية المتوفرة
        row4_frame = ttk.Frame(info_card)
        row4_frame.pack(fill=tk.X, padx=15, pady=(5, 15))

        ttk.Label(row4_frame, text="📊 الكمية المتوفرة:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.selected_item_qty = ttk.Label(row4_frame, text="-",
                                         foreground=ModernStyle.PRIMARY_COLOR,
                                         font=("Arial", 11, "bold"))
        self.selected_item_qty.pack(side=tk.LEFT, padx=(10, 0))

        # متغير لحفظ بيانات الصنف المحدد
        self.selected_item_data = None

    def create_add_controls_frame(self, parent):
        """إنشاء إطار التحكم في تفاصيل الإضافة"""
        controls_frame = ttk.LabelFrame(parent, text="⚙️ تفاصيل الإضافة", padding=15)
        controls_frame.pack(fill=tk.X, pady=(15, 0))

        # الصف الأول - الكمية والسعر
        row1_frame = ttk.Frame(controls_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 10))

        # الكمية
        qty_frame = ttk.Frame(row1_frame)
        qty_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(qty_frame, text="📦 الكمية:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)

        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = ttk.Entry(qty_frame, textvariable=self.quantity_var, width=12,
                                  font=ModernStyle.FONT_MEDIUM)
        quantity_entry.pack(side=tk.LEFT, padx=(10, 0))
        quantity_entry.bind('<KeyRelease>', self.calculate_preview)

        # السعر (قابل للتعديل)
        price_frame = ttk.Frame(row1_frame)
        price_frame.pack(side=tk.RIGHT, padx=(20, 0))

        ttk.Label(price_frame, text="💰 السعر:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)

        self.custom_price_var = tk.StringVar()
        price_entry = ttk.Entry(price_frame, textvariable=self.custom_price_var, width=12,
                               font=ModernStyle.FONT_MEDIUM)
        price_entry.pack(side=tk.LEFT, padx=(10, 0))
        price_entry.bind('<KeyRelease>', self.calculate_preview)

        # الصف الثاني - الضريبة والمعاينة
        row2_frame = ttk.Frame(controls_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 15))

        # الضريبة (قابلة للتعديل)
        tax_frame = ttk.Frame(row2_frame)
        tax_frame.pack(side=tk.LEFT)

        ttk.Label(tax_frame, text="🧾 الضريبة (%):",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)

        self.custom_tax_var = tk.StringVar()
        tax_entry = ttk.Entry(tax_frame, textvariable=self.custom_tax_var, width=8,
                             font=ModernStyle.FONT_MEDIUM)
        tax_entry.pack(side=tk.LEFT, padx=(10, 0))
        tax_entry.bind('<KeyRelease>', self.calculate_preview)

        # معاينة الإجمالي
        preview_frame = ttk.Frame(row2_frame)
        preview_frame.pack(side=tk.RIGHT, padx=(20, 0))

        ttk.Label(preview_frame, text="💵 الإجمالي المتوقع:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)

        self.preview_total_label = ttk.Label(preview_frame, text="0.00 ريال",
                                           font=("Arial", 11, "bold"),
                                           foreground=ModernStyle.PRIMARY_COLOR)
        self.preview_total_label.pack(side=tk.LEFT, padx=(10, 0))

        # الصف الثالث - الأزرار
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.pack(fill=tk.X)

        # زر الإضافة
        add_btn = ttk.Button(buttons_frame, text="➕ إضافة للفاتورة",
                           command=self.add_item_to_invoice, style="Primary.TButton")
        add_btn.pack(side=tk.LEFT)

        # زر مسح التحديد
        clear_btn = ttk.Button(buttons_frame, text="🗑️ مسح التحديد",
                             command=self.clear_selection, style="Secondary.TButton")
        clear_btn.pack(side=tk.LEFT, padx=(10, 0))

        # زر إعادة تعيين القيم الافتراضية
        reset_btn = ttk.Button(buttons_frame, text="🔄 القيم الافتراضية",
                             command=self.reset_to_defaults, style="Secondary.TButton")
        reset_btn.pack(side=tk.LEFT, padx=(10, 0))

    def clear_selection(self):
        """مسح التحديد الحالي"""
        self.barcode_var.set("")
        self.items_combo_var.set("")
        self.quantity_var.set("1")
        self.custom_price_var.set("")
        self.custom_tax_var.set("")
        self.preview_total_label.config(text="0.00 ريال")
        self.update_item_details(None)

    def reset_to_defaults(self):
        """إعادة تعيين القيم الافتراضية للصنف المحدد"""
        if self.selected_item_data:
            self.custom_price_var.set(str(self.selected_item_data['price']))
            tax_percentage = self.selected_item_data.get('tax_rate', 0) * 100
            self.custom_tax_var.set(str(tax_percentage))
            self.calculate_preview()

    def calculate_preview(self, event=None):
        """حساب معاينة الإجمالي"""
        try:
            quantity = float(self.quantity_var.get() or 0)
            price = float(self.custom_price_var.get() or 0)
            tax_rate = float(self.custom_tax_var.get() or 0) / 100

            subtotal = quantity * price
            tax_amount = subtotal * tax_rate
            total = subtotal + tax_amount

            self.preview_total_label.config(text=f"{total:.2f} ريال")
        except ValueError:
            self.preview_total_label.config(text="0.00 ريال")

    def update_item_details(self, item_data):
        """تحديث عرض تفاصيل الصنف المحدد"""
        if item_data:
            self.selected_item_data = item_data

            # تحديث اسم الصنف
            self.selected_item_name.config(text=item_data['name'],
                                         foreground=ModernStyle.TEXT_PRIMARY)

            # تحديث الباركود
            barcode_text = f"🏷️ {item_data['barcode']}"
            self.selected_item_barcode.config(text=barcode_text)

            # تحديث الوصف
            description = item_data.get('description', '') or 'لا يوجد وصف'
            self.selected_item_desc.config(text=description,
                                         foreground=ModernStyle.TEXT_PRIMARY)

            # تحديث السعر
            price_text = f"{item_data['price']:.2f} ريال"
            self.selected_item_price.config(text=price_text)

            # تحديث الكمية المتوفرة
            quantity = item_data.get('quantity', 0)
            quantity_str = f"{quantity:g}"

            # تحديد لون الكمية حسب المخزون
            if quantity == 0:
                qty_color = ModernStyle.DANGER_COLOR
                qty_text = f"{quantity_str} (نفد المخزون)"
            elif quantity <= 5:
                qty_color = ModernStyle.WARNING_COLOR
                qty_text = f"{quantity_str} (مخزون منخفض)"
            else:
                qty_color = ModernStyle.SUCCESS_COLOR
                qty_text = quantity_str

            self.selected_item_qty.config(text=qty_text, foreground=qty_color)

            tax_percentage = item_data.get('tax_rate', 0) * 100
            tax_text = f"{tax_percentage:.1f}%"
            self.selected_item_tax.config(text=tax_text, foreground=ModernStyle.TEXT_PRIMARY)

            # تحديث القيم في إطار التحكم
            self.custom_price_var.set(str(item_data['price']))
            self.custom_tax_var.set(str(tax_percentage))

            # تحديث حقل الكمية بالكمية المتوفرة للصنف
            available_quantity = item_data.get('quantity', 0)
            self.quantity_var.set(str(available_quantity))

            self.calculate_preview()
        else:
            # مسح التفاصيل
            self.selected_item_data = None
            self.selected_item_name.config(text="لم يتم اختيار صنف",
                                         foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_barcode.config(text="")
            self.selected_item_desc.config(text="-",
                                         foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_price.config(text="-",
                                          foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_qty.config(text="-",
                                        foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_tax.config(text="-",
                                        foreground=ModernStyle.TEXT_SECONDARY)

            # مسح حقول الإدخال
            self.quantity_var.set("0")
            self.custom_price_var.set("0")
            self.custom_tax_var.set("15")

    def create_items_table_frame(self, parent):
        """إنشاء إطار جدول الأصناف"""
        table_frame = ttk.LabelFrame(parent, text="🧾 أصناف الفاتورة", padding=20)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إطار الجدول والأزرار
        table_container = ttk.Frame(table_frame)
        table_container.pack(fill=tk.BOTH, expand=True)

        # إطار الجدول
        tree_frame = ttk.Frame(table_container)
        tree_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # إنشاء الجدول
        columns = ("الاسم", "السعر", "الكمية", "الإجمالي الفرعي", "الضريبة", "الإجمالي")
        self.items_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=10)

        # تعريف العناوين
        self.items_tree.heading("الاسم", text="اسم الصنف")
        self.items_tree.heading("السعر", text="السعر")
        self.items_tree.heading("الكمية", text="الكمية")
        self.items_tree.heading("الإجمالي الفرعي", text="الإجمالي الفرعي")
        self.items_tree.heading("الضريبة", text="الضريبة")
        self.items_tree.heading("الإجمالي", text="الإجمالي")

        # تعريف عرض الأعمدة
        self.items_tree.column("الاسم", width=200, anchor=tk.E)
        self.items_tree.column("السعر", width=100, anchor=tk.CENTER)
        self.items_tree.column("الكمية", width=80, anchor=tk.CENTER)
        self.items_tree.column("الإجمالي الفرعي", width=120, anchor=tk.CENTER)
        self.items_tree.column("الضريبة", width=100, anchor=tk.CENTER)
        self.items_tree.column("الإجمالي", width=120, anchor=tk.CENTER)

        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        # تخطيط الجدول
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الأزرار الجانبية
        buttons_side_frame = ttk.Frame(table_container)
        buttons_side_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # أزرار التحكم في الأصناف
        ttk.Label(buttons_side_frame, text="التحكم في الأصناف",
                 font=ModernStyle.FONT_MEDIUM).pack(pady=(0, 10))

        edit_qty_btn = ttk.Button(buttons_side_frame, text="✏️ تعديل الكمية",
                                command=self.edit_quantity, style="Secondary.TButton")
        edit_qty_btn.pack(fill=tk.X, pady=(0, 5))

        remove_item_btn = ttk.Button(buttons_side_frame, text="🗑️ حذف الصنف",
                                   command=self.remove_selected_item, style="Accent.TButton")
        remove_item_btn.pack(fill=tk.X, pady=(0, 5))

        # فاصل
        ttk.Separator(buttons_side_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        # أزرار إضافية
        ttk.Label(buttons_side_frame, text="عمليات إضافية",
                 font=ModernStyle.FONT_MEDIUM).pack(pady=(0, 10))

        clear_all_btn = ttk.Button(buttons_side_frame, text="🗑️ مسح الكل",
                                 command=self.clear_all_items, style="Accent.TButton")
        clear_all_btn.pack(fill=tk.X, pady=(0, 5))

        # قائمة السياق للحذف
        self.create_context_menu()
        self.items_tree.bind("<Button-3>", self.show_context_menu)
        self.items_tree.bind("<Double-1>", lambda e: self.edit_quantity())
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="حذف الصنف", command=self.remove_selected_item)
        self.context_menu.add_command(label="تعديل الكمية", command=self.edit_quantity)
    
    def create_totals_frame(self, parent):
        """إنشاء إطار الإجماليات"""
        totals_frame = ttk.LabelFrame(parent, text="💰 ملخص الفاتورة", padding=20)
        totals_frame.pack(fill=tk.X, pady=(0, 15))

        # إطار رئيسي للإجماليات
        main_totals_frame = ttk.Frame(totals_frame)
        main_totals_frame.pack(fill=tk.X)

        # العمود الأيسر - معلومات إضافية
        left_info_frame = ttk.Frame(main_totals_frame)
        left_info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # عدد الأصناف
        items_count_frame = ttk.Frame(left_info_frame)
        items_count_frame.pack(anchor=tk.W, pady=(0, 5))

        ttk.Label(items_count_frame, text="📦 عدد الأصناف:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.items_count_label = ttk.Label(items_count_frame, text="0",
                                         font=ModernStyle.FONT_MEDIUM,
                                         foreground=ModernStyle.PRIMARY_COLOR)
        self.items_count_label.pack(side=tk.LEFT, padx=(10, 0))

        # إجمالي الكميات
        total_qty_frame = ttk.Frame(left_info_frame)
        total_qty_frame.pack(anchor=tk.W)

        ttk.Label(total_qty_frame, text="📊 إجمالي الكميات:",
                 font=ModernStyle.FONT_MEDIUM).pack(side=tk.LEFT)
        self.total_qty_label = ttk.Label(total_qty_frame, text="0",
                                       font=ModernStyle.FONT_MEDIUM,
                                       foreground=ModernStyle.PRIMARY_COLOR)
        self.total_qty_label.pack(side=tk.LEFT, padx=(10, 0))

        # العمود الأيمن - الإجماليات المالية
        totals_grid = ttk.Frame(main_totals_frame)
        totals_grid.pack(side=tk.RIGHT, padx=(20, 0))

        # الإجمالي الفرعي
        ttk.Label(totals_grid, text="💵 الإجمالي الفرعي:",
                 font=ModernStyle.FONT_MEDIUM).grid(row=0, column=0, sticky=tk.E, padx=(0, 15), pady=3)
        self.subtotal_label = ttk.Label(totals_grid, text="0.00 ريال",
                                       font=ModernStyle.FONT_MEDIUM,
                                       foreground=ModernStyle.TEXT_PRIMARY)
        self.subtotal_label.grid(row=0, column=1, sticky=tk.W, pady=3)

        # إجمالي الضريبة
        ttk.Label(totals_grid, text="🧾 إجمالي الضريبة:",
                 font=ModernStyle.FONT_MEDIUM).grid(row=1, column=0, sticky=tk.E, padx=(0, 15), pady=3)
        self.tax_label = ttk.Label(totals_grid, text="0.00 ريال",
                                  font=ModernStyle.FONT_MEDIUM,
                                  foreground=ModernStyle.TEXT_PRIMARY)
        self.tax_label.grid(row=1, column=1, sticky=tk.W, pady=3)

        # فاصل
        ttk.Separator(totals_grid, orient=tk.HORIZONTAL).grid(row=2, column=0, columnspan=2,
                                                            sticky=tk.EW, pady=8)

        # الإجمالي الكلي
        ttk.Label(totals_grid, text="💰 الإجمالي الكلي:",
                 font=ModernStyle.FONT_LARGE).grid(row=3, column=0, sticky=tk.E, padx=(0, 15))
        self.total_label = ttk.Label(totals_grid, text="0.00 ريال",
                                    font=ModernStyle.FONT_LARGE,
                                    foreground=ModernStyle.PRIMARY_COLOR)
        self.total_label.grid(row=3, column=1, sticky=tk.W)
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار معلومات الحالة"""
        status_frame = ttk.LabelFrame(parent, text="📊 حالة النظام", padding=15)
        status_frame.pack(fill=tk.X, pady=(15, 0))

        # معلومات الحالة
        status_container = ttk.Frame(status_frame)
        status_container.pack(fill=tk.X)

        # حالة الفاتورة الحالية
        self.status_label = ttk.Label(status_container,
                                    text="✅ جاهز لإنشاء فاتورة جديدة",
                                    foreground=ModernStyle.SUCCESS_COLOR,
                                    font=ModernStyle.FONT_MEDIUM)
        self.status_label.pack(side=tk.LEFT)

        # معلومات إضافية
        info_frame = ttk.Frame(status_container)
        info_frame.pack(side=tk.RIGHT)

        # عدد الأصناف في الفاتورة الحالية
        self.items_count_label = ttk.Label(info_frame,
                                         text="📦 الأصناف: 0",
                                         foreground=ModernStyle.TEXT_SECONDARY,
                                         font=ModernStyle.FONT_SMALL)
        self.items_count_label.pack(side=tk.RIGHT, padx=(15, 0))

        # إجمالي الفاتورة
        self.quick_total_label = ttk.Label(info_frame,
                                         text="💰 الإجمالي: 0.00 ريال",
                                         foreground=ModernStyle.TEXT_SECONDARY,
                                         font=ModernStyle.FONT_SMALL)
        self.quick_total_label.pack(side=tk.RIGHT, padx=(15, 0))

    def update_status_info(self):
        """تحديث معلومات الحالة"""
        items_count = len(self.current_invoice.items)
        total_amount = self.current_invoice.total_amount

        # تحديث عدد الأصناف
        self.items_count_label.config(text=f"📦 الأصناف: {items_count}")

        # تحديث الإجمالي السريع
        self.quick_total_label.config(text=f"💰 الإجمالي: {total_amount:.2f} ريال")

        # تحديث حالة النظام
        if items_count == 0:
            self.status_label.config(text="✅ جاهز لإنشاء فاتورة جديدة",
                                   foreground=ModernStyle.SUCCESS_COLOR)
        elif items_count < 5:
            self.status_label.config(text="📝 جاري إنشاء الفاتورة...",
                                   foreground=ModernStyle.WARNING_COLOR)
        else:
            self.status_label.config(text="📋 فاتورة جاهزة للحفظ",
                                   foreground=ModernStyle.PRIMARY_COLOR)

    def export_to_excel(self):
        """تصدير الفاتورة إلى Excel"""
        if not self.current_invoice.items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة للتصدير")
            return

        try:
            from tkinter import filedialog
            import pandas as pd
            from datetime import datetime

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ الفاتورة كملف Excel"
            )

            if filename:
                # إعداد البيانات
                data = []
                for item_data in self.current_invoice.items:
                    item = item_data['item']
                    quantity = item_data['quantity']
                    subtotal = item.price * quantity
                    tax_amount = subtotal * item.tax_rate
                    total = subtotal + tax_amount

                    data.append({
                        'اسم الصنف': item.name,
                        'الوصف': item.description,
                        'الباركود': item.barcode,
                        'السعر': item.price,
                        'الكمية': quantity,
                        'الإجمالي الفرعي': subtotal,
                        'نسبة الضريبة %': item.tax_rate * 100,
                        'مبلغ الضريبة': tax_amount,
                        'الإجمالي الكلي': total
                    })

                # إنشاء DataFrame
                df = pd.DataFrame(data)

                # حفظ الملف
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='الفاتورة', index=False)

                    # إضافة ملخص الفاتورة
                    summary_data = {
                        'البيان': ['الإجمالي الفرعي', 'إجمالي الضريبة', 'الإجمالي الكلي', 'تاريخ الإنشاء'],
                        'القيمة': [
                            f"{self.current_invoice.subtotal:.2f} ريال",
                            f"{self.current_invoice.tax_amount:.2f} ريال",
                            f"{self.current_invoice.total:.2f} ريال",
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        ]
                    }
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='ملخص الفاتورة', index=False)

                messagebox.showinfo("نجح", f"تم تصدير الفاتورة بنجاح إلى:\n{filename}")

        except ImportError:
            messagebox.showerror("خطأ", "يجب تثبيت مكتبة pandas لتصدير Excel:\npip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير الفاتورة:\n{str(e)}")

    def load_items_combo(self):
        """تحميل الأصناف في القائمة المنسدلة"""
        items = self.db_manager.get_all_items()
        items_list = [f"{item['name']} - {item['barcode']}" for item in items]
        self.items_combo['values'] = items_list

    def search_by_barcode(self, event=None):
        """البحث عن صنف بالباركود"""
        barcode = self.barcode_var.get().strip()
        if not barcode:
            self.update_item_details(None)  # مسح التفاصيل
            return

        item_data = self.db_manager.get_item_by_barcode(barcode)
        if item_data:
            # تحديث تفاصيل الصنف
            self.update_item_details(item_data)
        else:
            self.update_item_details(None)  # مسح التفاصيل
            messagebox.showerror("خطأ", f"لم يتم العثور على صنف بالباركود: {barcode}")

    def on_item_select(self, event=None):
        """معالج اختيار صنف من القائمة المنسدلة"""
        selection = self.items_combo_var.get()
        if not selection:
            self.update_item_details(None)
            return

        # استخراج الباركود من النص المحدد
        barcode = selection.split(" - ")[-1]
        item_data = self.db_manager.get_item_by_barcode(barcode)

        if item_data:
            # عرض معلومات الصنف في حقل الباركود وتحديث التفاصيل
            self.barcode_var.set(barcode)
            self.update_item_details(item_data)
        else:
            self.update_item_details(None)

    def add_item_to_invoice(self):
        """إضافة صنف للفاتورة مع التفاصيل المخصصة"""
        # التحقق من وجود صنف محدد
        if not self.selected_item_data:
            messagebox.showerror("خطأ", "يجب اختيار صنف أولاً")
            return

        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "يجب أن تكون الكمية أكبر من صفر")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال كمية صحيحة")
            return

        # التحقق من السعر المخصص
        try:
            custom_price = float(self.custom_price_var.get())
            if custom_price <= 0:
                messagebox.showerror("خطأ", "يجب أن يكون السعر أكبر من صفر")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال سعر صحيح")
            return

        # التحقق من الضريبة المخصصة
        try:
            custom_tax_rate = float(self.custom_tax_var.get()) / 100
            if custom_tax_rate < 0 or custom_tax_rate > 1:
                messagebox.showerror("خطأ", "يجب أن تكون نسبة الضريبة بين 0 و 100")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال نسبة ضريبة صحيحة")
            return

        # التحقق من توفر الكمية المطلوبة
        available_quantity = self.selected_item_data.get('quantity', 0)
        if quantity > available_quantity:
            messagebox.showerror("خطأ",
                               f"الكمية المطلوبة ({quantity:g}) أكبر من المتوفر ({available_quantity:g})")
            return

        # إنشاء كائن Item بالقيم المخصصة
        item = Item(
            item_id=self.selected_item_data['item_id'],
            name=self.selected_item_data['name'],
            description=self.selected_item_data['description'] or "",
            price=custom_price,  # استخدام السعر المخصص
            barcode=self.selected_item_data['barcode'],
            tax_rate=custom_tax_rate,  # استخدام الضريبة المخصصة
            quantity=self.selected_item_data.get('quantity', 0)
        )

        # إضافة الصنف للفاتورة
        success = self.current_invoice.add_item(item, quantity)
        if success:
            self.update_items_table()
            self.update_totals()

            # رسالة تأكيد مفصلة
            subtotal = quantity * custom_price
            tax_amount = subtotal * custom_tax_rate
            total_with_tax = subtotal + tax_amount

            success_msg = f"""تم إضافة الصنف بنجاح:

📦 الصنف: {item.name}
📊 الكمية: {quantity:g}
💰 السعر: {custom_price:.2f} ريال
🧾 الضريبة: {custom_tax_rate*100:.1f}%
💵 الإجمالي الفرعي: {subtotal:.2f} ريال
💸 مبلغ الضريبة: {tax_amount:.2f} ريال
💳 الإجمالي الكلي: {total_with_tax:.2f} ريال"""

            # مسح الحقول وتفاصيل الصنف
            self.clear_selection()
            messagebox.showinfo("✅ تم بنجاح", success_msg)
        else:
            messagebox.showerror("خطأ", "فشل في إضافة الصنف")

    def add_item_to_invoice_direct(self, item: Item):
        """إضافة صنف للفاتورة مباشرة (للباركود)"""
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                quantity = 1.0
        except ValueError:
            quantity = 1.0

        success = self.current_invoice.add_item(item, quantity)
        if success:
            self.update_items_table()
            self.update_totals()
            self.quantity_var.set("1")
            messagebox.showinfo("نجح", f"تم إضافة {quantity:g} من {item.name} للفاتورة")
        else:
            messagebox.showerror("خطأ", "فشل في إضافة الصنف")

    def update_items_table(self):
        """تحديث جدول الأصناف"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة الأصناف
        for invoice_item in self.current_invoice.items:
            total_with_tax = invoice_item.get_total_with_tax()
            quantity_str = f"{invoice_item.quantity:g}"  # تنسيق الكمية لإزالة الأصفار غير الضرورية
            self.items_tree.insert("", tk.END, values=(
                invoice_item.name,
                f"{invoice_item.unit_price:.2f}",
                quantity_str,
                f"{invoice_item.subtotal:.2f}",
                f"{invoice_item.tax_amount:.2f}",
                f"{total_with_tax:.2f}"
            ), tags=(str(invoice_item.item_id),))

    def update_totals(self):
        """تحديث الإجماليات"""
        subtotal = self.current_invoice.get_subtotal()
        tax_total = self.current_invoice.total_tax
        total = self.current_invoice.total_amount

        # حساب عدد الأصناف وإجمالي الكميات
        items_count = len(self.current_invoice.items)
        total_quantity = sum(item.quantity for item in self.current_invoice.items)

        # تحديث التسميات المالية
        self.subtotal_label.config(text=f"{subtotal:.2f} ريال")
        self.tax_label.config(text=f"{tax_total:.2f} ريال")
        self.total_label.config(text=f"{total:.2f} ريال")

        # تحديث معلومات الأصناف والكميات
        self.items_count_label.config(text=str(items_count))
        self.total_qty_label.config(text=f"{total_quantity:g}")

        # تحديث معلومات الحالة
        self.update_status_info()

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        selection = self.items_tree.selection()
        if selection:
            self.context_menu.post(event.x_root, event.y_root)

    def remove_selected_item(self):
        """حذف الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            return

        # الحصول على معرف الصنف
        item_values = self.items_tree.item(selection[0])['values']
        item_name = item_values[0]

        # البحث عن الصنف في الفاتورة وحذفه
        for invoice_item in self.current_invoice.items:
            if invoice_item.name == item_name:
                self.current_invoice.remove_item(invoice_item.item_id)
                break

        self.update_items_table()
        self.update_totals()

    def edit_quantity(self):
        """تعديل كمية الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            return

        # الحصول على معلومات الصنف
        item_values = self.items_tree.item(selection[0])['values']
        item_name = item_values[0]
        current_quantity = item_values[2]

        # نافذة إدخال الكمية الجديدة
        new_quantity_str = tk.simpledialog.askstring(
            "تعديل الكمية",
            f"الكمية الحالية: {current_quantity}\nأدخل الكمية الجديدة:",
            initialvalue=str(current_quantity)
        )

        if new_quantity_str:
            try:
                new_quantity = float(new_quantity_str)
                if new_quantity <= 0:
                    messagebox.showerror("خطأ", "يجب أن تكون الكمية أكبر من صفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يجب إدخال كمية صحيحة")
                return

            # البحث عن الصنف وتحديث الكمية
            for invoice_item in self.current_invoice.items:
                if invoice_item.name == item_name:
                    self.current_invoice.update_item_quantity(invoice_item.item_id, new_quantity)
                    break

            self.update_items_table()
            self.update_totals()

    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من وجود أصناف في الفاتورة
            if self.current_invoice.is_empty():
                messagebox.showerror("⚠️ تحذير", "لا يمكن حفظ فاتورة فارغة\nيرجى إضافة أصناف أولاً")
                return

            # التحقق من معلومات العميل
            customer_name = self.customer_name_var.get().strip()
            if not customer_name:
                result = messagebox.askyesno("❓ تأكيد",
                                           "لم يتم تحديد اسم العميل.\nهل تريد المتابعة بدون اسم العميل؟")
                if not result:
                    return
                customer_name = "عميل غير محدد"

            # تحديث معلومات العميل في الفاتورة
            self.current_invoice.customer_name = customer_name

            # طباعة معلومات التشخيص
            print(f"🔍 محاولة حفظ الفاتورة:")
            print(f"   📦 عدد الأصناف: {len(self.current_invoice.items)}")
            print(f"   👤 العميل: {customer_name}")
            print(f"   🆔 معرف العميل: {self.selected_customer_id}")
            print(f"   💰 الإجمالي: {self.current_invoice.total_amount:.2f}")

            # إنشاء الفاتورة في قاعدة البيانات
            invoice_id = self.db_manager.create_invoice(
                customer_id=self.selected_customer_id,
                customer_name=customer_name
            )

            if not invoice_id:
                messagebox.showerror("❌ خطأ في قاعدة البيانات",
                                   "فشل في إنشاء الفاتورة في قاعدة البيانات\n" +
                                   "يرجى التحقق من اتصال قاعدة البيانات")
                return

            print(f"✅ تم إنشاء الفاتورة برقم: {invoice_id}")
            self.current_invoice.invoice_id = invoice_id

            # إضافة أصناف الفاتورة
            success = True
            failed_items = []

            print(f"📦 بدء حفظ {len(self.current_invoice.items)} صنف...")

            for i, invoice_item in enumerate(self.current_invoice.items):
                print(f"   💾 حفظ الصنف {i+1}: {invoice_item.name} (الكمية: {invoice_item.quantity:g})")

                item_success = self.db_manager.add_invoice_item(
                    invoice_id=invoice_id,
                    item_id=invoice_item.item_id,
                    quantity=invoice_item.quantity,
                    unit_price=invoice_item.unit_price,
                    item_tax_rate=invoice_item.item_tax_rate,
                    subtotal=invoice_item.subtotal,
                    tax_amount=invoice_item.tax_amount
                )

                if not item_success:
                    success = False
                    failed_items.append(invoice_item.name)
                    print(f"   ❌ فشل في حفظ الصنف: {invoice_item.name}")
                else:
                    print(f"   ✅ تم حفظ الصنف: {invoice_item.name}")

            if success:
                # تحديث إجماليات الفاتورة
                totals_success = self.db_manager.update_invoice_totals(
                    invoice_id,
                    self.current_invoice.total_amount,
                    self.current_invoice.total_tax
                )

                if totals_success:
                    print(f"✅ تم حفظ الفاتورة بالكامل بنجاح!")

                    messagebox.showinfo("✅ تم الحفظ بنجاح",
                                      f"تم حفظ الفاتورة بنجاح\n" +
                                      f"🧾 رقم الفاتورة: {invoice_id}\n" +
                                      f"👤 العميل: {customer_name}\n" +
                                      f"📦 عدد الأصناف: {len(self.current_invoice.items)}\n" +
                                      f"💰 الإجمالي: {self.current_invoice.total_amount:.2f} ريال")

                    # إنشاء فاتورة جديدة
                    self.current_invoice = Invoice()
                    self.customer_name_var.set("")
                    self.customer_combo.set("اختر عميل...")
                    self.selected_customer_id = None
                    self.update_items_table()
                    self.update_totals()
                else:
                    messagebox.showerror("❌ خطأ في الإجماليات",
                                       "تم حفظ الأصناف ولكن فشل في تحديث الإجماليات")
            else:
                error_msg = f"فشل في حفظ الأصناف التالية:\n" + "\n".join(failed_items)
                messagebox.showerror("❌ خطأ في حفظ الأصناف", error_msg)

        except Exception as e:
            print(f"❌ خطأ غير متوقع في حفظ الفاتورة: {str(e)}")
            messagebox.showerror("❌ خطأ غير متوقع",
                               f"حدث خطأ غير متوقع أثناء حفظ الفاتورة:\n{str(e)}")

    def preview_invoice(self):
        """معاينة الفاتورة"""
        if self.current_invoice.is_empty():
            messagebox.showerror("خطأ", "لا يمكن معاينة فاتورة فارغة")
            return

        # إنشاء نافذة معاينة
        self.show_print_preview()

    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        if not self.current_invoice.is_empty():
            result = messagebox.askyesno("تأكيد",
                                       "هل تريد إنشاء فاتورة جديدة؟ سيتم فقدان البيانات الحالية غير المحفوظة.")
            if not result:
                return

        # مسح الفاتورة الحالية
        self.clear_invoice()
        messagebox.showinfo("نجح", "تم إنشاء فاتورة جديدة")

    def print_invoice(self):
        """طباعة الفاتورة في المتصفح بتنسيق جميل"""
        if self.current_invoice.is_empty():
            messagebox.showerror("⚠️ تحذير", "لا يمكن طباعة فاتورة فارغة\nيرجى إضافة أصناف أولاً")
            return

        try:
            # استيراد مولد HTML
            from invoice_html_generator import InvoiceHTMLGenerator

            # تحديث معلومات العميل في الفاتورة
            customer_name = self.customer_name_var.get().strip()
            if customer_name:
                self.current_invoice.customer_name = customer_name

            # إنشاء مولد HTML
            html_generator = InvoiceHTMLGenerator()

            # إنشاء وفتح الفاتورة في المتصفح
            file_path = html_generator.create_and_open_invoice(self.current_invoice)

            # رسالة نجاح
            messagebox.showinfo("✅ تم بنجاح",
                              f"تم فتح الفاتورة في المتصفح بنجاح!\n" +
                              f"📁 مسار الملف: {file_path}\n\n" +
                              f"💡 يمكنك الآن طباعة الفاتورة من المتصفح باستخدام Ctrl+P")

        except Exception as e:
            print(f"❌ خطأ في طباعة الفاتورة: {str(e)}")
            messagebox.showerror("❌ خطأ في الطباعة",
                               f"فشل في فتح الفاتورة في المتصفح:\n{str(e)}\n\n" +
                               f"💡 تأكد من وجود متصفح ويب مثبت على النظام")

            # العودة للطريقة القديمة في حالة الفشل
            self.show_print_preview()

    def show_print_preview(self):
        """عرض معاينة الطباعة"""
        preview_window = tk.Toplevel(self.window)
        preview_window.title("معاينة الطباعة")
        preview_window.geometry("600x800")
        preview_window.configure(bg="white")

        # إطار المحتوى
        content_frame = ttk.Frame(preview_window)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان الفاتورة
        title_label = tk.Label(content_frame, text="فاتورة عرض سعر",
                              font=("Arial", 18, "bold"), bg="white")
        title_label.pack(pady=(0, 20))

        # معلومات الفاتورة
        info_text = f"""
التاريخ: {self.current_invoice.invoice_date}
العميل: {self.current_invoice.customer_name or "غير محدد"}
رقم الفاتورة: {self.current_invoice.invoice_id or "جديدة"}
        """

        info_label = tk.Label(content_frame, text=info_text,
                             font=("Arial", 12), bg="white", justify=tk.RIGHT)
        info_label.pack(anchor=tk.E, pady=(0, 20))

        # جدول الأصناف
        items_text = "الأصناف:\n" + "-" * 60 + "\n"
        items_text += f"{'الصنف':<20} {'الكمية':<8} {'السعر':<10} {'الإجمالي':<12}\n"
        items_text += "-" * 60 + "\n"

        for item in self.current_invoice.items:
            items_text += f"{item.name:<20} {item.quantity:<8} {item.unit_price:<10.2f} {item.get_total_with_tax():<12.2f}\n"

        items_text += "-" * 60 + "\n"
        items_text += f"الإجمالي الفرعي: {self.current_invoice.get_subtotal():.2f} ريال\n"
        items_text += f"إجمالي الضريبة: {self.current_invoice.total_tax:.2f} ريال\n"
        items_text += f"الإجمالي الكلي: {self.current_invoice.total_amount:.2f} ريال\n"

        items_label = tk.Label(content_frame, text=items_text,
                              font=("Courier", 10), bg="white", justify=tk.LEFT)
        items_label.pack(anchor=tk.W)

        # أزرار الطباعة
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(side=tk.BOTTOM, pady=20)

        print_btn = ttk.Button(buttons_frame, text="طباعة",
                             command=lambda: self.do_print(preview_window))
        print_btn.pack(side=tk.LEFT, padx=(0, 10))

        close_btn = ttk.Button(buttons_frame, text="إغلاق",
                             command=preview_window.destroy)
        close_btn.pack(side=tk.LEFT)

    def do_print(self, preview_window):
        """تنفيذ الطباعة الفعلية"""
        try:
            # يمكن هنا إضافة كود الطباعة الفعلي
            # مثل تصدير إلى PDF أو إرسال للطابعة
            messagebox.showinfo("طباعة", "تم إرسال الفاتورة للطباعة")
            preview_window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"فشل في الطباعة: {str(e)}")

    def clear_invoice(self):
        """مسح الفاتورة الحالية"""
        if not self.current_invoice.is_empty():
            result = messagebox.askyesno("تأكيد المسح",
                                       "هل أنت متأكد من مسح الفاتورة الحالية؟")
            if result:
                self.current_invoice.clear()
                self.customer_name_var.set("")
                self.customer_combo.set("اختر عميل...")
                self.selected_customer_id = None
                self.update_items_table()
                self.update_totals()
        else:
            messagebox.showinfo("معلومة", "الفاتورة فارغة بالفعل")

    def load_customers_combo(self):
        """تحميل العملاء في القائمة المنسدلة"""
        try:
            customers = self.db_manager.get_all_customers()
            customer_list = ["اختر عميل..."]
            self.customers_data = {}

            for customer in customers:
                display_name = f"{customer['name']} - {customer['phone'] or 'بدون هاتف'}"
                customer_list.append(display_name)
                self.customers_data[display_name] = customer

            self.customer_combo['values'] = customer_list
            self.customer_combo.set("اختر عميل...")

        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def on_customer_select(self, event=None):
        """عند اختيار عميل من القائمة"""
        selected = self.customer_var.get()
        if selected and selected != "اختر عميل..." and selected in self.customers_data:
            customer = self.customers_data[selected]
            self.selected_customer_id = customer['customer_id']
            self.customer_name_var.set(customer['name'])
        else:
            self.selected_customer_id = None

    def add_new_customer(self):
        """فتح نافذة إضافة عميل جديد"""
        try:
            from customers_management import CustomersManagementWindow
            # فتح نافذة إدارة العملاء
            customers_window = CustomersManagementWindow(self.window, self.db_manager)

            # انتظار إغلاق النافذة ثم تحديث القائمة
            self.window.wait_window(customers_window.window)
            self.load_customers_combo()

        except Exception as e:
            print(f"خطأ في فتح نافذة العملاء: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()

    def edit_quantity(self):
        """تعديل كمية الصنف المحدد"""
        selected_item = self.items_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف لتعديل كميته")
            return

        # الحصول على بيانات الصنف المحدد
        item_values = self.items_tree.item(selected_item[0])['values']
        item_name = item_values[0]
        current_quantity = item_values[2]

        # نافذة إدخال الكمية الجديدة
        from tkinter import simpledialog
        new_quantity = simpledialog.askfloat("تعديل الكمية",
                                           f"الكمية الحالية للصنف '{item_name}': {current_quantity}\n\nأدخل الكمية الجديدة:",
                                           minvalue=0.01)

        if new_quantity is not None:
            # البحث عن الصنف في الفاتورة وتحديث كميته
            for item_id, invoice_item in self.current_invoice.items.items():
                if invoice_item.item.name == item_name:
                    if self.current_invoice.update_item_quantity(item_id, new_quantity):
                        self.update_items_table()
                        self.update_totals()
                        messagebox.showinfo("نجح", f"تم تحديث كمية الصنف '{item_name}' إلى {new_quantity:g}")
                    else:
                        messagebox.showerror("خطأ", "فشل في تحديث الكمية")
                    break

    def remove_selected_item(self):
        """حذف الصنف المحدد"""
        selected_item = self.items_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف لحذفه")
            return

        # الحصول على اسم الصنف
        item_values = self.items_tree.item(selected_item[0])['values']
        item_name = item_values[0]

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الصنف '{item_name}' من الفاتورة؟")

        if result:
            # البحث عن الصنف في الفاتورة وحذفه
            for item_id, invoice_item in self.current_invoice.items.items():
                if invoice_item.item.name == item_name:
                    if self.current_invoice.remove_item(item_id):
                        self.update_items_table()
                        self.update_totals()
                        messagebox.showinfo("نجح", f"تم حذف الصنف '{item_name}' من الفاتورة")
                    else:
                        messagebox.showerror("خطأ", "فشل في حذف الصنف")
                    break

    def clear_all_items(self):
        """مسح جميع الأصناف من الفاتورة"""
        if self.current_invoice.is_empty():
            messagebox.showinfo("معلومة", "الفاتورة فارغة بالفعل")
            return

        result = messagebox.askyesno("تأكيد المسح",
                                   "هل أنت متأكد من مسح جميع الأصناف من الفاتورة؟")

        if result:
            self.current_invoice.clear()
            self.update_items_table()
            self.update_totals()
            messagebox.showinfo("نجح", "تم مسح جميع الأصناف من الفاتورة")

    def load_existing_invoice(self, invoice_details):
        """تحميل فاتورة موجودة للتحرير"""
        try:
            # مسح الفاتورة الحالية
            self.current_invoice.clear()

            # تحديث معلومات الفاتورة الأساسية
            self.current_invoice.invoice_id = invoice_details['invoice_id']
            self.current_invoice.invoice_date = invoice_details['invoice_date']
            self.current_invoice.customer_name = invoice_details.get('display_customer_name',
                                                                   invoice_details.get('customer_name', ''))
            self.current_invoice.total_amount = invoice_details['total_amount']
            self.current_invoice.total_tax = invoice_details['total_tax']

            # تحديث حقل اسم العميل في الواجهة
            if hasattr(self, 'customer_name_var'):
                self.customer_name_var.set(self.current_invoice.customer_name)

            # تحميل أصناف الفاتورة
            for item_data in invoice_details['items']:
                # إنشاء كائن InvoiceItem
                invoice_item = InvoiceItem(
                    invoice_item_id=item_data.get('invoice_item_id'),
                    invoice_id=self.current_invoice.invoice_id,
                    item_id=item_data.get('item_id'),
                    quantity=float(item_data['quantity']),
                    unit_price=float(item_data['unit_price']),
                    item_tax_rate=float(item_data.get('tax_rate', 0.15)),
                    subtotal=float(item_data['subtotal']),
                    tax_amount=float(item_data['tax_amount']),
                    name=item_data['name'],
                    description=item_data.get('description', ''),
                    barcode=item_data.get('barcode', '')
                )

                # إضافة الصنف للفاتورة
                self.current_invoice.items.append(invoice_item)

            # تحديث الواجهة
            self.update_items_table()
            self.update_totals()

            # تحديث عنوان النافذة
            self.window.title(f"📄 تحرير الفاتورة رقم {self.current_invoice.invoice_id} - شاشة كاملة")

            print(f"✅ تم تحميل الفاتورة رقم {self.current_invoice.invoice_id} بنجاح")
            print(f"📦 عدد الأصناف: {len(self.current_invoice.items)}")
            print(f"💰 الإجمالي: {self.current_invoice.total_amount:.2f} ريال")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الفاتورة: {str(e)}")
            print(f"❌ خطأ في تحميل الفاتورة: {str(e)}")
