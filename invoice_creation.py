import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Optional
from database_manager import DatabaseManager
from models import Item, Invoice, InvoiceItem
from main_gui import ModernStyle


class InvoiceCreationWindow:
    """نافذة إنشاء فاتورة جديدة"""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        """تهيئة نافذة إنشاء الفاتورة"""
        self.parent = parent
        self.db_manager = db_manager
        self.current_invoice = Invoice()
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إنشاء فاتورة جديدة")
        self.window.geometry("1200x800")
        self.window.configure(bg=ModernStyle.BG_COLOR)
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(main_frame,
                               text="📄 إنشاء فاتورة عرض سعر جديدة",
                               font=ModernStyle.FONT_TITLE,
                               foreground=ModernStyle.TEXT_PRIMARY,
                               background=ModernStyle.BG_COLOR)
        title_label.pack(pady=(0, 20))
        
        # إطار معلومات الفاتورة
        self.create_invoice_info_frame(main_frame)
        
        # إطار إضافة الأصناف
        self.create_add_items_frame(main_frame)
        
        # إطار جدول الأصناف
        self.create_items_table_frame(main_frame)
        
        # إطار الإجماليات
        self.create_totals_frame(main_frame)
        
        # إطار الأزرار
        self.create_buttons_frame(main_frame)
    
    def create_invoice_info_frame(self, parent):
        """إنشاء إطار معلومات الفاتورة"""
        info_frame = ttk.LabelFrame(parent, text="معلومات الفاتورة", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # العميل
        customer_frame = ttk.Frame(info_frame)
        customer_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(customer_frame, text="العميل:").pack(side=tk.LEFT)

        # قائمة منسدلة للعملاء
        self.customer_var = tk.StringVar()
        self.customer_combo = ttk.Combobox(customer_frame, textvariable=self.customer_var,
                                         width=40, state="readonly")
        self.customer_combo.pack(side=tk.LEFT, padx=(10, 10))
        self.customer_combo.bind('<<ComboboxSelected>>', self.on_customer_select)

        # زر إضافة عميل جديد
        add_customer_btn = ttk.Button(customer_frame, text="عميل جديد",
                                    command=self.add_new_customer)
        add_customer_btn.pack(side=tk.LEFT, padx=(5, 20))

        # أو إدخال اسم مباشر
        manual_frame = ttk.Frame(info_frame)
        manual_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(manual_frame, text="أو أدخل اسم العميل:").pack(side=tk.LEFT)

        self.customer_name_var = tk.StringVar()
        customer_entry = ttk.Entry(manual_frame, textvariable=self.customer_name_var, width=30)
        customer_entry.pack(side=tk.LEFT, padx=(10, 20))

        # تحميل العملاء
        self.load_customers_combo()

        # تاريخ الفاتورة
        date_frame = ttk.Frame(info_frame)
        date_frame.pack(fill=tk.X)

        ttk.Label(date_frame, text="التاريخ:").pack(side=tk.LEFT)

        date_label = ttk.Label(date_frame, text=self.current_invoice.invoice_date,
                              foreground=ModernStyle.TEXT_SECONDARY,
                              background=ModernStyle.BG_COLOR)
        date_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_add_items_frame(self, parent):
        """إنشاء إطار إضافة الأصناف"""
        add_frame = ttk.LabelFrame(parent, text="إضافة الأصناف", padding=10)
        add_frame.pack(fill=tk.X, pady=(0, 10))
        
        # البحث بالباركود
        barcode_frame = ttk.Frame(add_frame)
        barcode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(barcode_frame, text="الباركود:").pack(side=tk.LEFT)
        
        self.barcode_var = tk.StringVar()
        barcode_entry = ttk.Entry(barcode_frame, textvariable=self.barcode_var, width=20)
        barcode_entry.pack(side=tk.LEFT, padx=(10, 0))
        barcode_entry.bind('<Return>', self.search_by_barcode)
        
        search_btn = ttk.Button(barcode_frame, text="بحث", command=self.search_by_barcode)
        search_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # أو البحث بالاسم
        name_frame = ttk.Frame(add_frame)
        name_frame.pack(fill=tk.X)
        
        ttk.Label(name_frame, text="أو اختر من القائمة:").pack(side=tk.LEFT)
        
        self.items_combo_var = tk.StringVar()
        self.items_combo = ttk.Combobox(name_frame, textvariable=self.items_combo_var, 
                                       width=40, state="readonly")
        self.items_combo.pack(side=tk.LEFT, padx=(10, 0))
        self.items_combo.bind('<<ComboboxSelected>>', self.on_item_select)
        
        # تحميل الأصناف في القائمة المنسدلة
        self.load_items_combo()

        # إطار عرض تفاصيل الصنف المحدد
        self.create_item_details_frame(add_frame)

        # متغيرات العميل
        self.selected_customer_id = None
        
        # الكمية
        ttk.Label(name_frame, text="الكمية:").pack(side=tk.LEFT, padx=(20, 0))
        
        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = ttk.Entry(name_frame, textvariable=self.quantity_var, width=10)
        quantity_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # زر الإضافة
        add_btn = ttk.Button(name_frame, text="إضافة للفاتورة", 
                           command=self.add_item_to_invoice, style="Primary.TButton")
        add_btn.pack(side=tk.LEFT, padx=(20, 0))

    def create_item_details_frame(self, parent):
        """إنشاء إطار عرض تفاصيل الصنف المحدد"""
        details_frame = ttk.LabelFrame(parent, text="تفاصيل الصنف المحدد", padding=10)
        details_frame.pack(fill=tk.X, pady=(10, 0))

        # إطار المعلومات الأساسية
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X)

        # العمود الأول - المعلومات الأساسية
        left_frame = ttk.Frame(info_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # اسم الصنف
        name_info_frame = ttk.Frame(left_frame)
        name_info_frame.pack(fill=tk.X, pady=2)
        ttk.Label(name_info_frame, text="اسم الصنف:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.selected_item_name = ttk.Label(name_info_frame, text="لم يتم اختيار صنف",
                                          foreground=ModernStyle.TEXT_SECONDARY)
        self.selected_item_name.pack(side=tk.LEFT, padx=(10, 0))

        # الوصف
        desc_info_frame = ttk.Frame(left_frame)
        desc_info_frame.pack(fill=tk.X, pady=2)
        ttk.Label(desc_info_frame, text="الوصف:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.selected_item_desc = ttk.Label(desc_info_frame, text="-",
                                          foreground=ModernStyle.TEXT_SECONDARY)
        self.selected_item_desc.pack(side=tk.LEFT, padx=(10, 0))

        # العمود الثاني - السعر والكمية
        right_frame = ttk.Frame(info_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # السعر
        price_info_frame = ttk.Frame(right_frame)
        price_info_frame.pack(fill=tk.X, pady=2)
        ttk.Label(price_info_frame, text="السعر:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.selected_item_price = ttk.Label(price_info_frame, text="-",
                                           foreground=ModernStyle.TEXT_SECONDARY)
        self.selected_item_price.pack(side=tk.LEFT, padx=(10, 0))

        # الكمية المتوفرة
        qty_info_frame = ttk.Frame(right_frame)
        qty_info_frame.pack(fill=tk.X, pady=2)
        ttk.Label(qty_info_frame, text="الكمية المتوفرة:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.selected_item_qty = ttk.Label(qty_info_frame, text="-",
                                         foreground=ModernStyle.TEXT_SECONDARY)
        self.selected_item_qty.pack(side=tk.LEFT, padx=(10, 0))

        # الضريبة
        tax_info_frame = ttk.Frame(right_frame)
        tax_info_frame.pack(fill=tk.X, pady=2)
        ttk.Label(tax_info_frame, text="نسبة الضريبة:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        self.selected_item_tax = ttk.Label(tax_info_frame, text="-",
                                         foreground=ModernStyle.TEXT_SECONDARY)
        self.selected_item_tax.pack(side=tk.LEFT, padx=(10, 0))

        # متغير لحفظ بيانات الصنف المحدد
        self.selected_item_data = None

    def update_item_details(self, item_data):
        """تحديث عرض تفاصيل الصنف المحدد"""
        if item_data:
            self.selected_item_data = item_data

            # تحديث المعلومات
            self.selected_item_name.config(text=item_data['name'], foreground=ModernStyle.TEXT_PRIMARY)

            description = item_data.get('description', '') or 'لا يوجد وصف'
            self.selected_item_desc.config(text=description, foreground=ModernStyle.TEXT_PRIMARY)

            price_text = f"{item_data['price']:.2f} ريال"
            self.selected_item_price.config(text=price_text, foreground=ModernStyle.TEXT_PRIMARY)

            quantity = item_data.get('quantity', 0)
            quantity_str = f"{quantity:g}"

            # تحديد لون الكمية حسب المخزون
            if quantity == 0:
                qty_color = ModernStyle.DANGER_COLOR
                qty_text = f"{quantity_str} (نفد المخزون)"
            elif quantity <= 5:
                qty_color = ModernStyle.WARNING_COLOR
                qty_text = f"{quantity_str} (مخزون منخفض)"
            else:
                qty_color = ModernStyle.SUCCESS_COLOR
                qty_text = quantity_str

            self.selected_item_qty.config(text=qty_text, foreground=qty_color)

            tax_percentage = item_data.get('tax_rate', 0) * 100
            tax_text = f"{tax_percentage:.1f}%"
            self.selected_item_tax.config(text=tax_text, foreground=ModernStyle.TEXT_PRIMARY)
        else:
            # مسح التفاصيل
            self.selected_item_data = None
            self.selected_item_name.config(text="لم يتم اختيار صنف", foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_desc.config(text="-", foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_price.config(text="-", foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_qty.config(text="-", foreground=ModernStyle.TEXT_SECONDARY)
            self.selected_item_tax.config(text="-", foreground=ModernStyle.TEXT_SECONDARY)

    def create_items_table_frame(self, parent):
        """إنشاء إطار جدول الأصناف"""
        table_frame = ttk.LabelFrame(parent, text="أصناف الفاتورة", padding=10)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # إنشاء الجدول
        columns = ("الاسم", "السعر", "الكمية", "الإجمالي الفرعي", "الضريبة", "الإجمالي")
        self.items_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعريف العناوين
        self.items_tree.heading("الاسم", text="اسم الصنف")
        self.items_tree.heading("السعر", text="السعر")
        self.items_tree.heading("الكمية", text="الكمية")
        self.items_tree.heading("الإجمالي الفرعي", text="الإجمالي الفرعي")
        self.items_tree.heading("الضريبة", text="الضريبة")
        self.items_tree.heading("الإجمالي", text="الإجمالي")
        
        # تعريف عرض الأعمدة
        self.items_tree.column("الاسم", width=200, anchor=tk.E)
        self.items_tree.column("السعر", width=100, anchor=tk.CENTER)
        self.items_tree.column("الكمية", width=80, anchor=tk.CENTER)
        self.items_tree.column("الإجمالي الفرعي", width=120, anchor=tk.CENTER)
        self.items_tree.column("الضريبة", width=100, anchor=tk.CENTER)
        self.items_tree.column("الإجمالي", width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        items_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # قائمة السياق للحذف
        self.create_context_menu()
        self.items_tree.bind("<Button-3>", self.show_context_menu)
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="حذف الصنف", command=self.remove_selected_item)
        self.context_menu.add_command(label="تعديل الكمية", command=self.edit_quantity)
    
    def create_totals_frame(self, parent):
        """إنشاء إطار الإجماليات"""
        totals_frame = ttk.LabelFrame(parent, text="الإجماليات", padding=15)
        totals_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إطار الإجماليات
        totals_grid = ttk.Frame(totals_frame)
        totals_grid.pack(side=tk.RIGHT)
        
        # الإجمالي الفرعي
        ttk.Label(totals_grid, text="الإجمالي الفرعي:", font=ModernStyle.FONT_MEDIUM).grid(row=0, column=0, sticky=tk.E, padx=(0, 10))
        self.subtotal_label = ttk.Label(totals_grid, text="0.00 ريال", font=ModernStyle.FONT_MEDIUM,
                                       foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.subtotal_label.grid(row=0, column=1, sticky=tk.W)
        
        # إجمالي الضريبة
        ttk.Label(totals_grid, text="إجمالي الضريبة:", font=ModernStyle.FONT_MEDIUM).grid(row=1, column=0, sticky=tk.E, padx=(0, 10), pady=5)
        self.tax_label = ttk.Label(totals_grid, text="0.00 ريال", font=ModernStyle.FONT_MEDIUM,
                                  foreground=ModernStyle.TEXT_PRIMARY, background=ModernStyle.BG_COLOR)
        self.tax_label.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # الإجمالي الكلي
        ttk.Label(totals_grid, text="الإجمالي الكلي:", font=ModernStyle.FONT_LARGE).grid(row=2, column=0, sticky=tk.E, padx=(0, 10))
        self.total_label = ttk.Label(totals_grid, text="0.00 ريال", font=ModernStyle.FONT_LARGE,
                                    foreground=ModernStyle.PRIMARY_COLOR, background=ModernStyle.BG_COLOR)
        self.total_label.grid(row=2, column=1, sticky=tk.W)
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # زر حفظ الفاتورة
        save_btn = ttk.Button(buttons_frame, text="💾 حفظ الفاتورة", 
                            command=self.save_invoice, style="Primary.TButton")
        save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر طباعة الفاتورة
        print_btn = ttk.Button(buttons_frame, text="🖨️ طباعة", 
                             command=self.print_invoice, style="Secondary.TButton")
        print_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر مسح الفاتورة
        clear_btn = ttk.Button(buttons_frame, text="🗑️ مسح الفاتورة", 
                             command=self.clear_invoice, style="Accent.TButton")
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر إغلاق
        close_btn = ttk.Button(buttons_frame, text="إغلاق", 
                             command=self.window.destroy)
        close_btn.pack(side=tk.RIGHT)
    
    def load_items_combo(self):
        """تحميل الأصناف في القائمة المنسدلة"""
        items = self.db_manager.get_all_items()
        items_list = [f"{item['name']} - {item['barcode']}" for item in items]
        self.items_combo['values'] = items_list

    def search_by_barcode(self, event=None):
        """البحث عن صنف بالباركود"""
        barcode = self.barcode_var.get().strip()
        if not barcode:
            self.update_item_details(None)  # مسح التفاصيل
            return

        item_data = self.db_manager.get_item_by_barcode(barcode)
        if item_data:
            # تحديث تفاصيل الصنف
            self.update_item_details(item_data)
        else:
            self.update_item_details(None)  # مسح التفاصيل
            messagebox.showerror("خطأ", f"لم يتم العثور على صنف بالباركود: {barcode}")

    def on_item_select(self, event=None):
        """معالج اختيار صنف من القائمة المنسدلة"""
        selection = self.items_combo_var.get()
        if not selection:
            self.update_item_details(None)
            return

        # استخراج الباركود من النص المحدد
        barcode = selection.split(" - ")[-1]
        item_data = self.db_manager.get_item_by_barcode(barcode)

        if item_data:
            # عرض معلومات الصنف في حقل الباركود وتحديث التفاصيل
            self.barcode_var.set(barcode)
            self.update_item_details(item_data)
        else:
            self.update_item_details(None)

    def add_item_to_invoice(self):
        """إضافة صنف للفاتورة من القائمة المنسدلة"""
        # التحقق من وجود صنف محدد
        if not self.selected_item_data:
            messagebox.showerror("خطأ", "يجب اختيار صنف أولاً")
            return

        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "يجب أن تكون الكمية أكبر من صفر")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال كمية صحيحة")
            return

        # التحقق من توفر الكمية المطلوبة
        available_quantity = self.selected_item_data.get('quantity', 0)
        if quantity > available_quantity:
            messagebox.showerror("خطأ",
                               f"الكمية المطلوبة ({quantity:g}) أكبر من المتوفر ({available_quantity:g})")
            return

        # إنشاء كائن Item
        item = Item(
            item_id=self.selected_item_data['item_id'],
            name=self.selected_item_data['name'],
            description=self.selected_item_data['description'] or "",
            price=self.selected_item_data['price'],
            barcode=self.selected_item_data['barcode'],
            tax_rate=self.selected_item_data['tax_rate'],
            quantity=self.selected_item_data.get('quantity', 0)
        )

        # إضافة الصنف للفاتورة
        success = self.current_invoice.add_item(item, quantity)
        if success:
            self.update_items_table()
            self.update_totals()
            # مسح الحقول وتفاصيل الصنف
            self.barcode_var.set("")
            self.quantity_var.set("1")
            self.items_combo_var.set("")
            self.update_item_details(None)
            messagebox.showinfo("نجح", f"تم إضافة {quantity:g} من {item.name} للفاتورة")
        else:
            messagebox.showerror("خطأ", "فشل في إضافة الصنف")

    def add_item_to_invoice_direct(self, item: Item):
        """إضافة صنف للفاتورة مباشرة (للباركود)"""
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                quantity = 1.0
        except ValueError:
            quantity = 1.0

        success = self.current_invoice.add_item(item, quantity)
        if success:
            self.update_items_table()
            self.update_totals()
            self.quantity_var.set("1")
            messagebox.showinfo("نجح", f"تم إضافة {quantity:g} من {item.name} للفاتورة")
        else:
            messagebox.showerror("خطأ", "فشل في إضافة الصنف")

    def update_items_table(self):
        """تحديث جدول الأصناف"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة الأصناف
        for invoice_item in self.current_invoice.items:
            total_with_tax = invoice_item.get_total_with_tax()
            quantity_str = f"{invoice_item.quantity:g}"  # تنسيق الكمية لإزالة الأصفار غير الضرورية
            self.items_tree.insert("", tk.END, values=(
                invoice_item.name,
                f"{invoice_item.unit_price:.2f}",
                quantity_str,
                f"{invoice_item.subtotal:.2f}",
                f"{invoice_item.tax_amount:.2f}",
                f"{total_with_tax:.2f}"
            ), tags=(str(invoice_item.item_id),))

    def update_totals(self):
        """تحديث الإجماليات"""
        subtotal = self.current_invoice.get_subtotal()
        tax_total = self.current_invoice.total_tax
        total = self.current_invoice.total_amount

        self.subtotal_label.config(text=f"{subtotal:.2f} ريال")
        self.tax_label.config(text=f"{tax_total:.2f} ريال")
        self.total_label.config(text=f"{total:.2f} ريال")

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        selection = self.items_tree.selection()
        if selection:
            self.context_menu.post(event.x_root, event.y_root)

    def remove_selected_item(self):
        """حذف الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            return

        # الحصول على معرف الصنف
        item_values = self.items_tree.item(selection[0])['values']
        item_name = item_values[0]

        # البحث عن الصنف في الفاتورة وحذفه
        for invoice_item in self.current_invoice.items:
            if invoice_item.name == item_name:
                self.current_invoice.remove_item(invoice_item.item_id)
                break

        self.update_items_table()
        self.update_totals()

    def edit_quantity(self):
        """تعديل كمية الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            return

        # الحصول على معلومات الصنف
        item_values = self.items_tree.item(selection[0])['values']
        item_name = item_values[0]
        current_quantity = item_values[2]

        # نافذة إدخال الكمية الجديدة
        new_quantity_str = tk.simpledialog.askstring(
            "تعديل الكمية",
            f"الكمية الحالية: {current_quantity}\nأدخل الكمية الجديدة:",
            initialvalue=str(current_quantity)
        )

        if new_quantity_str:
            try:
                new_quantity = float(new_quantity_str)
                if new_quantity <= 0:
                    messagebox.showerror("خطأ", "يجب أن تكون الكمية أكبر من صفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يجب إدخال كمية صحيحة")
                return

            # البحث عن الصنف وتحديث الكمية
            for invoice_item in self.current_invoice.items:
                if invoice_item.name == item_name:
                    self.current_invoice.update_item_quantity(invoice_item.item_id, new_quantity)
                    break

            self.update_items_table()
            self.update_totals()

    def save_invoice(self):
        """حفظ الفاتورة"""
        if self.current_invoice.is_empty():
            messagebox.showerror("خطأ", "لا يمكن حفظ فاتورة فارغة")
            return

        # تحديث معلومات العميل
        customer_name = self.customer_name_var.get().strip()
        self.current_invoice.customer_name = customer_name

        # إنشاء الفاتورة في قاعدة البيانات مع معرف العميل
        invoice_id = self.db_manager.create_invoice(
            customer_id=self.selected_customer_id,
            customer_name=customer_name
        )

        if not invoice_id:
            messagebox.showerror("خطأ", "فشل في إنشاء الفاتورة")
            return

        self.current_invoice.invoice_id = invoice_id

        # إضافة أصناف الفاتورة
        success = True
        for invoice_item in self.current_invoice.items:
            item_success = self.db_manager.add_invoice_item(
                invoice_id=invoice_id,
                item_id=invoice_item.item_id,
                quantity=invoice_item.quantity,
                unit_price=invoice_item.unit_price,
                item_tax_rate=invoice_item.item_tax_rate,
                subtotal=invoice_item.subtotal,
                tax_amount=invoice_item.tax_amount
            )
            if not item_success:
                success = False
                break

        if success:
            # تحديث إجماليات الفاتورة
            self.db_manager.update_invoice_totals(
                invoice_id,
                self.current_invoice.total_amount,
                self.current_invoice.total_tax
            )

            messagebox.showinfo("نجح", f"تم حفظ الفاتورة بنجاح\nرقم الفاتورة: {invoice_id}")

            # إنشاء فاتورة جديدة
            self.current_invoice = Invoice()
            self.customer_name_var.set("")
            self.customer_combo.set("اختر عميل...")
            self.selected_customer_id = None
            self.update_items_table()
            self.update_totals()
        else:
            messagebox.showerror("خطأ", "فشل في حفظ أصناف الفاتورة")

    def print_invoice(self):
        """طباعة الفاتورة"""
        if self.current_invoice.is_empty():
            messagebox.showerror("خطأ", "لا يمكن طباعة فاتورة فارغة")
            return

        # إنشاء نافذة معاينة الطباعة
        self.show_print_preview()

    def show_print_preview(self):
        """عرض معاينة الطباعة"""
        preview_window = tk.Toplevel(self.window)
        preview_window.title("معاينة الطباعة")
        preview_window.geometry("600x800")
        preview_window.configure(bg="white")

        # إطار المحتوى
        content_frame = ttk.Frame(preview_window)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان الفاتورة
        title_label = tk.Label(content_frame, text="فاتورة عرض سعر",
                              font=("Arial", 18, "bold"), bg="white")
        title_label.pack(pady=(0, 20))

        # معلومات الفاتورة
        info_text = f"""
التاريخ: {self.current_invoice.invoice_date}
العميل: {self.current_invoice.customer_name or "غير محدد"}
رقم الفاتورة: {self.current_invoice.invoice_id or "جديدة"}
        """

        info_label = tk.Label(content_frame, text=info_text,
                             font=("Arial", 12), bg="white", justify=tk.RIGHT)
        info_label.pack(anchor=tk.E, pady=(0, 20))

        # جدول الأصناف
        items_text = "الأصناف:\n" + "-" * 60 + "\n"
        items_text += f"{'الصنف':<20} {'الكمية':<8} {'السعر':<10} {'الإجمالي':<12}\n"
        items_text += "-" * 60 + "\n"

        for item in self.current_invoice.items:
            items_text += f"{item.name:<20} {item.quantity:<8} {item.unit_price:<10.2f} {item.get_total_with_tax():<12.2f}\n"

        items_text += "-" * 60 + "\n"
        items_text += f"الإجمالي الفرعي: {self.current_invoice.get_subtotal():.2f} ريال\n"
        items_text += f"إجمالي الضريبة: {self.current_invoice.total_tax:.2f} ريال\n"
        items_text += f"الإجمالي الكلي: {self.current_invoice.total_amount:.2f} ريال\n"

        items_label = tk.Label(content_frame, text=items_text,
                              font=("Courier", 10), bg="white", justify=tk.LEFT)
        items_label.pack(anchor=tk.W)

        # أزرار الطباعة
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(side=tk.BOTTOM, pady=20)

        print_btn = ttk.Button(buttons_frame, text="طباعة",
                             command=lambda: self.do_print(preview_window))
        print_btn.pack(side=tk.LEFT, padx=(0, 10))

        close_btn = ttk.Button(buttons_frame, text="إغلاق",
                             command=preview_window.destroy)
        close_btn.pack(side=tk.LEFT)

    def do_print(self, preview_window):
        """تنفيذ الطباعة الفعلية"""
        try:
            # يمكن هنا إضافة كود الطباعة الفعلي
            # مثل تصدير إلى PDF أو إرسال للطابعة
            messagebox.showinfo("طباعة", "تم إرسال الفاتورة للطباعة")
            preview_window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"فشل في الطباعة: {str(e)}")

    def clear_invoice(self):
        """مسح الفاتورة الحالية"""
        if not self.current_invoice.is_empty():
            result = messagebox.askyesno("تأكيد المسح",
                                       "هل أنت متأكد من مسح الفاتورة الحالية؟")
            if result:
                self.current_invoice.clear()
                self.customer_name_var.set("")
                self.customer_combo.set("اختر عميل...")
                self.selected_customer_id = None
                self.update_items_table()
                self.update_totals()
        else:
            messagebox.showinfo("معلومة", "الفاتورة فارغة بالفعل")

    def load_customers_combo(self):
        """تحميل العملاء في القائمة المنسدلة"""
        try:
            customers = self.db_manager.get_all_customers()
            customer_list = ["اختر عميل..."]
            self.customers_data = {}

            for customer in customers:
                display_name = f"{customer['name']} - {customer['phone'] or 'بدون هاتف'}"
                customer_list.append(display_name)
                self.customers_data[display_name] = customer

            self.customer_combo['values'] = customer_list
            self.customer_combo.set("اختر عميل...")

        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def on_customer_select(self, event=None):
        """عند اختيار عميل من القائمة"""
        selected = self.customer_var.get()
        if selected and selected != "اختر عميل..." and selected in self.customers_data:
            customer = self.customers_data[selected]
            self.selected_customer_id = customer['customer_id']
            self.customer_name_var.set(customer['name'])
        else:
            self.selected_customer_id = None

    def add_new_customer(self):
        """فتح نافذة إضافة عميل جديد"""
        try:
            from customers_management import CustomersManagementWindow
            # فتح نافذة إدارة العملاء
            customers_window = CustomersManagementWindow(self.window, self.db_manager)

            # انتظار إغلاق النافذة ثم تحديث القائمة
            self.window.wait_window(customers_window.window)
            self.load_customers_combo()

        except Exception as e:
            print(f"خطأ في فتح نافذة العملاء: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
