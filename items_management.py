import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Optional
import os
from database_manager import DatabaseManager
from models import Item
from main_gui import ModernStyle

try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False


class ItemsManagementWindow:
    """نافذة إدارة الأصناف"""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        """تهيئة نافذة إدارة الأصناف"""
        self.parent = parent
        self.db_manager = db_manager
        self.selected_item_id = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_items()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الأصناف")
        self.window.geometry("1200x800")  # زيادة الحجم لإظهار جميع الأزرار
        self.window.configure(bg=ModernStyle.BG_COLOR)
        self.window.transient(self.parent)
        self.window.grab_set()

        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(main_frame,
                               text="📦 إدارة الأصناف",
                               font=ModernStyle.FONT_TITLE,
                               foreground=ModernStyle.TEXT_PRIMARY,
                               background=ModernStyle.BG_COLOR)
        title_label.pack(pady=(0, 20))
        
        # إطار البحث
        self.create_search_frame(main_frame)
        
        # إطار الجدول والأزرار
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # إطار الجدول
        self.create_table_frame(content_frame)
        
        # إطار النموذج
        self.create_form_frame(content_frame)
    
    def create_search_frame(self, parent):
        """إنشاء إطار البحث"""
        search_frame = ttk.LabelFrame(parent, text="البحث", padding=10)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل البحث
        search_label = ttk.Label(search_frame, text="البحث:")
        search_label.pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # زر مسح البحث
        clear_btn = ttk.Button(search_frame, text="مسح", command=self.clear_search)
        clear_btn.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_table_frame(self, parent):
        """إنشاء إطار الجدول"""
        table_frame = ttk.LabelFrame(parent, text="قائمة الأصناف", padding=10)
        table_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # إنشاء الجدول
        columns = ("ID", "الاسم", "السعر", "الباركود", "الضريبة %")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        self.tree.heading("ID", text="المعرف")
        self.tree.heading("الاسم", text="اسم الصنف")
        self.tree.heading("السعر", text="السعر (ريال)")
        self.tree.heading("الباركود", text="الباركود")
        self.tree.heading("الضريبة %", text="الضريبة %")
        
        # تعريف عرض الأعمدة
        self.tree.column("ID", width=60, anchor=tk.CENTER)
        self.tree.column("الاسم", width=200, anchor=tk.E)
        self.tree.column("السعر", width=100, anchor=tk.CENTER)
        self.tree.column("الباركود", width=120, anchor=tk.CENTER)
        self.tree.column("الضريبة %", width=80, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def create_form_frame(self, parent):
        """إنشاء إطار النموذج"""
        form_frame = ttk.LabelFrame(parent, text="بيانات الصنف", padding=15)
        form_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=(10, 0))

        # تحديد عرض ثابت للإطار
        form_frame.configure(width=350)
        
        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.price_var = tk.StringVar()
        self.barcode_var = tk.StringVar()
        self.tax_rate_var = tk.StringVar()
        
        # حقول النموذج
        row = 0
        
        # اسم الصنف
        ttk.Label(form_frame, text="اسم الصنف:").grid(row=row, column=0, sticky=tk.E, pady=5)
        ttk.Entry(form_frame, textvariable=self.name_var, width=25).grid(row=row, column=1, padx=(10, 0), pady=5)
        row += 1
        
        # الوصف
        ttk.Label(form_frame, text="الوصف:").grid(row=row, column=0, sticky=tk.E, pady=5)
        ttk.Entry(form_frame, textvariable=self.description_var, width=25).grid(row=row, column=1, padx=(10, 0), pady=5)
        row += 1
        
        # السعر
        ttk.Label(form_frame, text="السعر (ريال):").grid(row=row, column=0, sticky=tk.E, pady=5)
        ttk.Entry(form_frame, textvariable=self.price_var, width=25).grid(row=row, column=1, padx=(10, 0), pady=5)
        row += 1
        
        # الباركود
        ttk.Label(form_frame, text="الباركود:").grid(row=row, column=0, sticky=tk.E, pady=5)
        ttk.Entry(form_frame, textvariable=self.barcode_var, width=25).grid(row=row, column=1, padx=(10, 0), pady=5)
        row += 1
        
        # نسبة الضريبة
        ttk.Label(form_frame, text="الضريبة (%):").grid(row=row, column=0, sticky=tk.E, pady=5)
        ttk.Entry(form_frame, textvariable=self.tax_rate_var, width=25).grid(row=row, column=1, padx=(10, 0), pady=5)
        row += 1
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(form_frame)
        buttons_frame.grid(row=row, column=0, columnspan=2, pady=20, sticky="ew")

        # إعداد الشبكة للأزرار
        buttons_frame.columnconfigure(0, weight=1)

        # مجموعة أزرار العمليات الأساسية
        basic_frame = ttk.LabelFrame(buttons_frame, text="العمليات الأساسية", padding=10)
        basic_frame.pack(fill=tk.X, pady=(0, 10))

        # زر إضافة
        add_btn = ttk.Button(basic_frame, text="➕ إضافة صنف",
                           command=self.add_item, style="Primary.TButton")
        add_btn.pack(pady=3, fill=tk.X)

        # زر تحديث
        update_btn = ttk.Button(basic_frame, text="✏️ تحديث الصنف",
                              command=self.update_item, style="Secondary.TButton")
        update_btn.pack(pady=3, fill=tk.X)

        # زر حذف
        delete_btn = ttk.Button(basic_frame, text="🗑️ حذف الصنف",
                              command=self.delete_item, style="Accent.TButton")
        delete_btn.pack(pady=3, fill=tk.X)

        # زر مسح النموذج
        clear_btn = ttk.Button(basic_frame, text="🧹 مسح النموذج",
                             command=self.clear_form)
        clear_btn.pack(pady=3, fill=tk.X)

        # مجموعة أزرار Excel
        excel_frame = ttk.LabelFrame(buttons_frame, text="عمليات Excel", padding=10)
        excel_frame.pack(fill=tk.X, pady=(0, 10))

        # زر تصدير نموذج Excel
        export_template_btn = ttk.Button(excel_frame, text="📋 تصدير نموذج Excel",
                                       command=self.export_excel_template,
                                       style="Success.TButton")
        export_template_btn.pack(pady=3, fill=tk.X)

        # زر استيراد من Excel
        import_btn = ttk.Button(excel_frame, text="📊 استيراد من Excel",
                              command=self.import_from_excel, style="Success.TButton")
        import_btn.pack(pady=3, fill=tk.X)

        # زر إغلاق
        close_frame = ttk.Frame(buttons_frame)
        close_frame.pack(fill=tk.X, pady=(10, 0))

        close_btn = ttk.Button(close_frame, text="❌ إغلاق",
                             command=self.window.destroy)
        close_btn.pack(pady=5, fill=tk.X)
    
    def load_items(self):
        """تحميل الأصناف في الجدول"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # جلب الأصناف
        items = self.db_manager.get_all_items()
        
        # إضافة الأصناف للجدول
        for item in items:
            tax_percentage = item['tax_rate'] * 100
            self.tree.insert("", tk.END, values=(
                item['item_id'],
                item['name'],
                f"{item['price']:.2f}",
                item['barcode'],
                f"{tax_percentage:.1f}"
            ))
    
    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        search_term = self.search_var.get().strip()
        
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # جلب النتائج
        if search_term:
            items = self.db_manager.search_items(search_term)
        else:
            items = self.db_manager.get_all_items()
        
        # إضافة النتائج للجدول
        for item in items:
            tax_percentage = item['tax_rate'] * 100
            self.tree.insert("", tk.END, values=(
                item['item_id'],
                item['name'],
                f"{item['price']:.2f}",
                item['barcode'],
                f"{tax_percentage:.1f}"
            ))
    
    def clear_search(self):
        """مسح البحث"""
        self.search_var.set("")
    
    def on_item_select(self, event):
        """معالج تحديد صنف من الجدول"""
        selection = self.tree.selection()
        if selection:
            item_values = self.tree.item(selection[0])['values']
            self.selected_item_id = item_values[0]
            
            # ملء النموذج ببيانات الصنف المحدد
            item_data = self.db_manager.get_item_by_id(self.selected_item_id)
            if item_data:
                self.name_var.set(item_data['name'])
                self.description_var.set(item_data['description'] or "")
                self.price_var.set(str(item_data['price']))
                self.barcode_var.set(item_data['barcode'])
                self.tax_rate_var.set(str(item_data['tax_rate'] * 100))
    
    def validate_form(self) -> bool:
        """التحقق من صحة بيانات النموذج"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم الصنف")
            return False
        
        if not self.barcode_var.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال الباركود")
            return False
        
        try:
            price = float(self.price_var.get())
            if price <= 0:
                messagebox.showerror("خطأ", "يجب أن يكون السعر أكبر من صفر")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال سعر صحيح")
            return False
        
        try:
            tax_rate = float(self.tax_rate_var.get() or "0")
            if tax_rate < 0 or tax_rate > 100:
                messagebox.showerror("خطأ", "يجب أن تكون نسبة الضريبة بين 0 و 100")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "يجب إدخال نسبة ضريبة صحيحة")
            return False
        
        return True

    def add_item(self):
        """إضافة صنف جديد"""
        if not self.validate_form():
            return

        # التحقق من عدم تكرار الباركود
        existing_item = self.db_manager.get_item_by_barcode(self.barcode_var.get().strip())
        if existing_item:
            messagebox.showerror("خطأ", "الباركود موجود مسبقاً")
            return

        # إضافة الصنف
        success = self.db_manager.add_item(
            name=self.name_var.get().strip(),
            description=self.description_var.get().strip(),
            price=float(self.price_var.get()),
            barcode=self.barcode_var.get().strip(),
            tax_rate=float(self.tax_rate_var.get() or "0") / 100
        )

        if success:
            messagebox.showinfo("نجح", "تم إضافة الصنف بنجاح")
            self.clear_form()
            self.load_items()
        else:
            messagebox.showerror("خطأ", "فشل في إضافة الصنف")

    def update_item(self):
        """تحديث صنف موجود"""
        if not self.selected_item_id:
            messagebox.showerror("خطأ", "يجب تحديد صنف للتحديث")
            return

        if not self.validate_form():
            return

        # التحقق من عدم تكرار الباركود (إلا إذا كان نفس الصنف)
        existing_item = self.db_manager.get_item_by_barcode(self.barcode_var.get().strip())
        if existing_item and existing_item['item_id'] != self.selected_item_id:
            messagebox.showerror("خطأ", "الباركود موجود مسبقاً")
            return

        # تحديث الصنف
        success = self.db_manager.update_item(
            item_id=self.selected_item_id,
            name=self.name_var.get().strip(),
            description=self.description_var.get().strip(),
            price=float(self.price_var.get()),
            barcode=self.barcode_var.get().strip(),
            tax_rate=float(self.tax_rate_var.get() or "0") / 100
        )

        if success:
            messagebox.showinfo("نجح", "تم تحديث الصنف بنجاح")
            self.clear_form()
            self.load_items()
        else:
            messagebox.showerror("خطأ", "فشل في تحديث الصنف")

    def delete_item(self):
        """حذف صنف"""
        if not self.selected_item_id:
            messagebox.showerror("خطأ", "يجب تحديد صنف للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا الصنف؟\nلا يمكن التراجع عن هذا الإجراء.")

        if result:
            success = self.db_manager.delete_item(self.selected_item_id)

            if success:
                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.clear_form()
                self.load_items()
            else:
                messagebox.showerror("خطأ", "فشل في حذف الصنف")

    def clear_form(self):
        """مسح النموذج"""
        self.name_var.set("")
        self.description_var.set("")
        self.price_var.set("")
        self.barcode_var.set("")
        self.tax_rate_var.set("")
        self.selected_item_id = None

        # إلغاء التحديد من الجدول
        for item in self.tree.selection():
            self.tree.selection_remove(item)

    def export_excel_template(self):
        """تصدير نموذج Excel للاستيراد"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")
            return

        try:
            # اختيار مجلد الحفظ فقط
            folder_path = filedialog.askdirectory(
                title="اختر مجلد لحفظ نموذج Excel"
            )

            if not folder_path:
                return

            # إنشاء مسار الملف
            file_path = os.path.join(folder_path, "Items_Import_Template.xlsx")

            # التحقق من وجود الملف مسبقاً
            if os.path.exists(file_path):
                result = messagebox.askyesno("ملف موجود",
                                           f"الملف موجود مسبقاً في:\n{file_path}\n\nهل تريد استبداله؟")
                if not result:
                    return

            # إنشاء ملف Excel جديد
            wb = Workbook()
            ws = wb.active
            ws.title = "الأصناف"

            # تنسيق العناوين
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            center_alignment = Alignment(horizontal="center", vertical="center")

            # العناوين
            headers = ["اسم الصنف", "الوصف", "السعر", "الباركود", "نسبة الضريبة %"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # بيانات تجريبية
            sample_data = [
                ["لابتوب ديل", "لابتوب ديل انسبايرون 15", "2500.00", "DEL001", "15"],
                ["ماوس لوجيتك", "ماوس لاسلكي", "85.50", "LOG001", "15"],
                ["لوحة مفاتيح", "لوحة مفاتيح عربي/انجليزي", "120.00", "KEY001", "15"],
                ["شاشة سامسونج", "شاشة 24 بوصة", "650.00", "SAM001", "15"],
                ["طابعة HP", "طابعة ليزر أبيض وأسود", "450.00", "HP001", "15"]
            ]

            for row, data in enumerate(sample_data, 2):
                for col, value in enumerate(data, 1):
                    ws.cell(row=row, column=col, value=value)

            # تعديل عرض الأعمدة
            column_widths = [25, 35, 15, 15, 20]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width

            # إضافة ورقة التعليمات
            instructions_ws = wb.create_sheet("التعليمات")
            instructions = [
                "تعليمات استيراد الأصناف:",
                "",
                "1. املأ البيانات في ورقة 'الأصناف'",
                "2. اسم الصنف: مطلوب",
                "3. الوصف: اختياري",
                "4. السعر: مطلوب، رقم أكبر من صفر",
                "5. الباركود: مطلوب، يجب أن يكون فريد",
                "6. نسبة الضريبة: اختياري، رقم بين 0 و 100",
                "",
                "ملاحظات:",
                "- لا تحذف الصف الأول (العناوين)",
                "- تأكد من عدم وجود باركود مكرر",
                "- استخدم الأرقام الإنجليزية فقط للسعر والضريبة",
                "- احفظ الملف بصيغة .xlsx"
            ]

            for row, instruction in enumerate(instructions, 1):
                instructions_ws.cell(row=row, column=1, value=instruction)

            # حفظ الملف
            wb.save(file_path)
            messagebox.showinfo("نجح", f"تم تصدير النموذج بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النموذج:\n{str(e)}")

    def import_from_excel(self):
        """استيراد الأصناف من ملف Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")
            return

        # اختيار الملف
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # قراءة الملف
            wb = load_workbook(file_path)

            # البحث عن ورقة الأصناف
            ws = None
            if "الأصناف" in wb.sheetnames:
                ws = wb["الأصناف"]
            elif "Items" in wb.sheetnames:
                ws = wb["Items"]
            else:
                ws = wb.active

            # قراءة البيانات
            items_data = []
            errors = []

            # تخطي الصف الأول (العناوين)
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                if not any(row):  # تخطي الصفوف الفارغة
                    continue

                try:
                    name = str(row[0]).strip() if row[0] else ""
                    description = str(row[1]).strip() if row[1] else ""
                    price = float(row[2]) if row[2] else 0.0
                    barcode = str(row[3]).strip() if row[3] else ""
                    tax_rate = float(row[4]) if row[4] else 0.0

                    # التحقق من البيانات المطلوبة
                    if not name:
                        errors.append(f"الصف {row_num}: اسم الصنف مطلوب")
                        continue

                    if not barcode:
                        errors.append(f"الصف {row_num}: الباركود مطلوب")
                        continue

                    if price <= 0:
                        errors.append(f"الصف {row_num}: السعر يجب أن يكون أكبر من صفر")
                        continue

                    if tax_rate < 0 or tax_rate > 100:
                        errors.append(f"الصف {row_num}: نسبة الضريبة يجب أن تكون بين 0 و 100")
                        continue

                    # التحقق من عدم تكرار الباركود
                    existing_item = self.db_manager.get_item_by_barcode(barcode)
                    if existing_item:
                        errors.append(f"الصف {row_num}: الباركود '{barcode}' موجود مسبقاً")
                        continue

                    # التحقق من عدم تكرار الباركود في نفس الملف
                    if any(item['barcode'] == barcode for item in items_data):
                        errors.append(f"الصف {row_num}: الباركود '{barcode}' مكرر في الملف")
                        continue

                    items_data.append({
                        'name': name,
                        'description': description,
                        'price': price,
                        'barcode': barcode,
                        'tax_rate': tax_rate / 100  # تحويل إلى نسبة عشرية
                    })

                except (ValueError, TypeError) as e:
                    errors.append(f"الصف {row_num}: خطأ في تنسيق البيانات - {str(e)}")

            # عرض الأخطاء إن وجدت
            if errors:
                error_message = "تم العثور على الأخطاء التالية:\n\n" + "\n".join(errors[:10])
                if len(errors) > 10:
                    error_message += f"\n... و {len(errors) - 10} أخطاء أخرى"

                if items_data:
                    error_message += f"\n\nهل تريد المتابعة واستيراد {len(items_data)} صنف صحيح؟"
                    result = messagebox.askyesno("أخطاء في البيانات", error_message)
                    if not result:
                        return
                else:
                    messagebox.showerror("خطأ", error_message)
                    return

            if not items_data:
                messagebox.showwarning("تحذير", "لم يتم العثور على بيانات صحيحة للاستيراد")
                return

            # تأكيد الاستيراد
            result = messagebox.askyesno("تأكيد الاستيراد",
                                       f"هل تريد استيراد {len(items_data)} صنف؟")
            if not result:
                return

            # استيراد البيانات
            success_count = 0
            failed_items = []

            for item in items_data:
                success = self.db_manager.add_item(
                    name=item['name'],
                    description=item['description'],
                    price=item['price'],
                    barcode=item['barcode'],
                    tax_rate=item['tax_rate']
                )

                if success:
                    success_count += 1
                else:
                    failed_items.append(item['name'])

            # تحديث الجدول
            self.load_items()

            # عرض النتائج
            if failed_items:
                message = f"تم استيراد {success_count} صنف بنجاح\n"
                message += f"فشل في استيراد {len(failed_items)} صنف:\n"
                message += "\n".join(failed_items[:5])
                if len(failed_items) > 5:
                    message += f"\n... و {len(failed_items) - 5} أصناف أخرى"
                messagebox.showwarning("نتائج الاستيراد", message)
            else:
                messagebox.showinfo("نجح", f"تم استيراد {success_count} صنف بنجاح!")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في قراءة الملف:\n{str(e)}")
