import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Tuple, Dict, Any


class DatabaseManager:
    """مدير قاعدة البيانات لبرنامج فاتورة عرض السعر"""
    
    def __init__(self, db_name: str = "invoice_system.db"):
        """تهيئة مدير قاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة"""
        self.db_name = db_name
        self._create_tables()
    
    def _get_connection(self) -> sqlite3.Connection:
        """إنشاء اتصال جديد بقاعدة البيانات"""
        conn = sqlite3.Connection(self.db_name)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def _execute_query(self, query: str, params: tuple = ()) -> bool:
        """تنفيذ استعلام SQL (INSERT, UPDATE, DELETE)"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return True
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return False
    
    def _fetch_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """تنفيذ استعلام SQL وجلب البيانات (SELECT)"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchall()
        except sqlite3.Error as e:
            print(f"خطأ في جلب البيانات: {e}")
            return []
    
    def _fetch_one(self, query: str, params: tuple = ()) -> Optional[sqlite3.Row]:
        """تنفيذ استعلام SQL وجلب سجل واحد"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchone()
        except sqlite3.Error as e:
            print(f"خطأ في جلب البيانات: {e}")
            return None
    
    def _create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        # جدول العملاء
        customers_table = """
        CREATE TABLE IF NOT EXISTS Customers (
            customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            tax_number TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الأصناف
        items_table = """
        CREATE TABLE IF NOT EXISTS Items (
            item_id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            price REAL NOT NULL CHECK(price > 0),
            barcode TEXT UNIQUE NOT NULL,
            tax_rate REAL DEFAULT 0.0 CHECK(tax_rate >= 0.0 AND tax_rate <= 1.0),
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول الفواتير (محدث لربطه بالعملاء)
        invoices_table = """
        CREATE TABLE IF NOT EXISTS Invoices (
            invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_date TEXT NOT NULL,
            customer_id INTEGER,
            customer_name TEXT,
            total_amount REAL DEFAULT 0.0,
            total_tax REAL DEFAULT 0.0,
            FOREIGN KEY (customer_id) REFERENCES Customers (customer_id)
        )
        """

        # جدول أصناف الفاتورة
        invoice_items_table = """
        CREATE TABLE IF NOT EXISTS InvoiceItems (
            invoice_item_id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            item_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL CHECK(quantity > 0),
            unit_price REAL NOT NULL CHECK(unit_price > 0),
            item_tax_rate REAL NOT NULL CHECK(item_tax_rate >= 0.0 AND item_tax_rate <= 1.0),
            subtotal REAL NOT NULL,
            tax_amount REAL NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES Invoices (invoice_id),
            FOREIGN KEY (item_id) REFERENCES Items (item_id)
        )
        """

        # تنفيذ إنشاء الجداول
        self._execute_query(customers_table)
        self._execute_query(items_table)
        self._execute_query(invoices_table)
        self._execute_query(invoice_items_table)
    
    # وظائف إدارة الأصناف
    def add_item(self, name: str, description: str, price: float, barcode: str, tax_rate: float = 0.0) -> bool:
        """إضافة صنف جديد"""
        query = """
        INSERT INTO Items (name, description, price, barcode, tax_rate)
        VALUES (?, ?, ?, ?, ?)
        """
        return self._execute_query(query, (name, description, price, barcode, tax_rate))
    
    def update_item(self, item_id: int, name: str = None, description: str = None, 
                   price: float = None, barcode: str = None, tax_rate: float = None) -> bool:
        """تحديث معلومات صنف موجود"""
        updates = []
        params = []
        
        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if description is not None:
            updates.append("description = ?")
            params.append(description)
        if price is not None:
            updates.append("price = ?")
            params.append(price)
        if barcode is not None:
            updates.append("barcode = ?")
            params.append(barcode)
        if tax_rate is not None:
            updates.append("tax_rate = ?")
            params.append(tax_rate)
        
        if not updates:
            return False
        
        params.append(item_id)
        query = f"UPDATE Items SET {', '.join(updates)} WHERE item_id = ?"
        return self._execute_query(query, tuple(params))
    
    def delete_item(self, item_id: int) -> bool:
        """حذف صنف"""
        query = "DELETE FROM Items WHERE item_id = ?"
        return self._execute_query(query, (item_id,))
    
    def get_item_by_id(self, item_id: int) -> Optional[Dict[str, Any]]:
        """جلب صنف بواسطة معرفه"""
        query = "SELECT * FROM Items WHERE item_id = ?"
        result = self._fetch_one(query, (item_id,))
        return dict(result) if result else None
    
    def get_item_by_barcode(self, barcode: str) -> Optional[Dict[str, Any]]:
        """جلب صنف بواسطة الباركود"""
        query = "SELECT * FROM Items WHERE barcode = ?"
        result = self._fetch_one(query, (barcode,))
        return dict(result) if result else None
    
    def get_all_items(self) -> List[Dict[str, Any]]:
        """جلب جميع الأصناف"""
        query = "SELECT * FROM Items ORDER BY name"
        results = self._fetch_query(query)
        return [dict(row) for row in results]
    
    def search_items(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث في الأصناف بالاسم أو الوصف أو الباركود"""
        query = """
        SELECT * FROM Items
        WHERE name LIKE ? OR description LIKE ? OR barcode LIKE ?
        ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        results = self._fetch_query(query, (search_pattern, search_pattern, search_pattern))
        return [dict(row) for row in results]

    # وظائف إدارة العملاء
    def add_customer(self, name: str, phone: str = "", email: str = "",
                    address: str = "", tax_number: str = "") -> bool:
        """إضافة عميل جديد"""
        query = """
        INSERT INTO Customers (name, phone, email, address, tax_number)
        VALUES (?, ?, ?, ?, ?)
        """
        return self._execute_query(query, (name, phone, email, address, tax_number))

    def update_customer(self, customer_id: int, name: str = None, phone: str = None,
                       email: str = None, address: str = None, tax_number: str = None) -> bool:
        """تحديث معلومات عميل موجود"""
        updates = []
        params = []

        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if phone is not None:
            updates.append("phone = ?")
            params.append(phone)
        if email is not None:
            updates.append("email = ?")
            params.append(email)
        if address is not None:
            updates.append("address = ?")
            params.append(address)
        if tax_number is not None:
            updates.append("tax_number = ?")
            params.append(tax_number)

        if not updates:
            return False

        params.append(customer_id)
        query = f"UPDATE Customers SET {', '.join(updates)} WHERE customer_id = ?"
        return self._execute_query(query, tuple(params))

    def delete_customer(self, customer_id: int) -> bool:
        """حذف عميل"""
        query = "DELETE FROM Customers WHERE customer_id = ?"
        return self._execute_query(query, (customer_id,))

    def get_customer_by_id(self, customer_id: int) -> Optional[Dict[str, Any]]:
        """جلب عميل بواسطة معرفه"""
        query = "SELECT * FROM Customers WHERE customer_id = ?"
        result = self._fetch_one(query, (customer_id,))
        return dict(result) if result else None

    def get_all_customers(self) -> List[Dict[str, Any]]:
        """جلب جميع العملاء"""
        query = "SELECT * FROM Customers ORDER BY name"
        results = self._fetch_query(query)
        return [dict(row) for row in results]

    def search_customers(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث في العملاء بالاسم أو الهاتف أو البريد الإلكتروني"""
        query = """
        SELECT * FROM Customers
        WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
        ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        results = self._fetch_query(query, (search_pattern, search_pattern, search_pattern))
        return [dict(row) for row in results]

    # وظائف إدارة الفواتير
    def create_invoice(self, customer_id: int = None, customer_name: str = "") -> Optional[int]:
        """إنشاء فاتورة جديدة وإرجاع معرف الفاتورة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        query = """
        INSERT INTO Invoices (invoice_date, customer_id, customer_name, total_amount, total_tax)
        VALUES (?, ?, ?, 0.0, 0.0)
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (current_time, customer_id, customer_name))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الفاتورة: {e}")
            return None

    def add_invoice_item(self, invoice_id: int, item_id: int, quantity: int,
                        unit_price: float, item_tax_rate: float,
                        subtotal: float, tax_amount: float) -> bool:
        """إضافة صنف إلى فاتورة"""
        query = """
        INSERT INTO InvoiceItems
        (invoice_id, item_id, quantity, unit_price, item_tax_rate, subtotal, tax_amount)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return self._execute_query(query, (invoice_id, item_id, quantity, unit_price,
                                         item_tax_rate, subtotal, tax_amount))

    def update_invoice_totals(self, invoice_id: int, total_amount: float, total_tax: float) -> bool:
        """تحديث إجماليات الفاتورة"""
        query = "UPDATE Invoices SET total_amount = ?, total_tax = ? WHERE invoice_id = ?"
        return self._execute_query(query, (total_amount, total_tax, invoice_id))

    def get_invoice_details(self, invoice_id: int) -> Optional[Dict[str, Any]]:
        """جلب تفاصيل فاتورة معينة مع أصنافها ومعلومات العميل"""
        # جلب معلومات الفاتورة مع معلومات العميل
        invoice_query = """
        SELECT i.*, c.name as customer_full_name, c.phone, c.email, c.address, c.tax_number
        FROM Invoices i
        LEFT JOIN Customers c ON i.customer_id = c.customer_id
        WHERE i.invoice_id = ?
        """
        invoice_result = self._fetch_one(invoice_query, (invoice_id,))

        if not invoice_result:
            return None

        invoice_data = dict(invoice_result)

        # جلب أصناف الفاتورة مع تفاصيل الأصناف
        items_query = """
        SELECT ii.*, i.name, i.description, i.barcode
        FROM InvoiceItems ii
        JOIN Items i ON ii.item_id = i.item_id
        WHERE ii.invoice_id = ?
        ORDER BY ii.invoice_item_id
        """
        items_results = self._fetch_query(items_query, (invoice_id,))
        invoice_data['items'] = [dict(row) for row in items_results]

        return invoice_data

    def get_all_invoices(self) -> List[Dict[str, Any]]:
        """جلب جميع الفواتير مع أسماء العملاء"""
        query = """
        SELECT i.*,
               COALESCE(c.name, i.customer_name, 'غير محدد') as display_customer_name
        FROM Invoices i
        LEFT JOIN Customers c ON i.customer_id = c.customer_id
        ORDER BY i.invoice_date DESC
        """
        results = self._fetch_query(query)
        return [dict(row) for row in results]

    def delete_invoice(self, invoice_id: int) -> bool:
        """حذف فاتورة وجميع أصنافها"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                # حذف أصناف الفاتورة أولاً
                cursor.execute("DELETE FROM InvoiceItems WHERE invoice_id = ?", (invoice_id,))
                # ثم حذف الفاتورة
                cursor.execute("DELETE FROM Invoices WHERE invoice_id = ?", (invoice_id,))
                conn.commit()
                return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف الفاتورة: {e}")
            return False

    def get_invoice_statistics(self) -> Dict[str, Any]:
        """جلب إحصائيات الفواتير"""
        stats = {}

        # عدد الفواتير
        count_query = "SELECT COUNT(*) as count FROM Invoices"
        count_result = self._fetch_one(count_query)
        stats['total_invoices'] = count_result['count'] if count_result else 0

        # إجمالي المبيعات
        total_query = "SELECT SUM(total_amount) as total FROM Invoices"
        total_result = self._fetch_one(total_query)
        stats['total_sales'] = total_result['total'] if total_result and total_result['total'] else 0.0

        # إجمالي الضرائب
        tax_query = "SELECT SUM(total_tax) as total_tax FROM Invoices"
        tax_result = self._fetch_one(tax_query)
        stats['total_taxes'] = tax_result['total_tax'] if tax_result and tax_result['total_tax'] else 0.0

        return stats
