# مخطط برنامج فاتورة عرض سعر بلغة البايثون

هذا المستند يقدم مخططاً تفصيلياً لبرنامج فاتورة عرض سعر، مع التركيز على دعم إضافة الأصناف عن طريق الباركود، دعم الضريبة، وتعدد الأصناف. يغطي المخطط تحليل المتطلبات، تصميم قاعدة البيانات، وتصميم مكونات البرنامج الرئيسية.









## تحليل المتطلبات وتصميم النظام

### الميزات الأساسية للبرنامج:
1.  **إنشاء فواتير عرض سعر:** القدرة على إنشاء فواتير عرض سعر جديدة.
2.  **إدارة الأصناف:**
    *   إضافة/تعديل/حذف الأصناف.
    *   ربط كل صنف بباركود فريد.
    *   تخزين معلومات الصنف (الاسم، السعر، الوصف، الباركود، الضريبة المطبقة).
3.  **إضافة الأصناف للفاتورة:**
    *   إضافة الأصناف يدوياً.
    *   إضافة الأصناف عن طريق مسح الباركود (يجب أن يقوم البرنامج بالبحث عن الصنف بالباركود وإضافته تلقائياً).
    *   تحديد الكمية لكل صنف في الفاتورة.
4.  **حساب الضريبة:**
    *   تطبيق الضريبة على الأصناف الفردية أو على إجمالي الفاتورة.
    *   إمكانية تحديد نسبة الضريبة.
5.  **تعدد الأصناف:** دعم إضافة عدد غير محدود من الأصناف إلى فاتورة واحدة.
6.  **حساب الإجمالي:** حساب الإجمالي الفرعي، إجمالي الضريبة، والإجمالي الكلي للفاتورة.
7.  **حفظ واستعراض الفواتير:** القدرة على حفظ فواتير عرض الأسعار واستعراضها لاحقاً.
8.  **واجهة مستخدم بسيطة:** واجهة سهلة الاستخدام لإدخال البيانات وعرض الفواتير (يمكن أن تكون واجهة سطر أوامر بسيطة أو واجهة رسومية لاحقاً).

### المكونات الرئيسية للنظام:
1.  **وحدة إدارة البيانات (Data Management Module):** مسؤولة عن التفاعل مع قاعدة البيانات (إضافة، تعديل، حذف، استعلام).
2.  **وحدة إدارة الأصناف (Item Management Module):** مسؤولة عن إضافة وتعديل وحذف الأصناف، والبحث عنها باستخدام الباركود.
3.  **وحدة إدارة الفواتير (Invoice Management Module):** مسؤولة عن إنشاء الفواتير، إضافة الأصناف إليها، حساب الإجماليات والضرائب، وحفظ الفواتير.
4.  **وحدة الواجهة (User Interface Module):** مسؤولة عن التفاعل مع المستخدم (إدخال البيانات، عرض النتائج).
5.  **وحدة الباركود (Barcode Module):** مسؤولة عن قراءة الباركود (افتراضياً، يمكن أن تكون مجرد دالة للبحث عن الصنف بالباركود المدخل يدوياً في هذه المرحلة).




## تصميم قاعدة البيانات

سنستخدم قاعدة بيانات SQLite لسهولتها في الاستخدام والتضمين في تطبيقات بايثون.

### الجداول المقترحة:

#### 1. جدول `Items` (الأصناف)
يحتوي هذا الجدول على معلومات عن كل صنف متاح.

| اسم الحقل     | نوع البيانات | الوصف                                   | ملاحظات        |
| :------------- | :----------- | :-------------------------------------- | :-------------- |
| `item_id`      | INTEGER      | معرف فريد للصنف                        | مفتاح أساسي، ترقيم تلقائي |
| `name`         | TEXT         | اسم الصنف                              | لا يمكن أن يكون فارغاً |
| `description`  | TEXT         | وصف تفصيلي للصنف (اختياري)             |                 |\n| `price`        | REAL         | سعر الوحدة للصنف                       | لا يمكن أن يكون فارغاً، أكبر من 0 |
| `barcode`      | TEXT         | رمز الباركود الفريد للصنف              | فريد، لا يمكن أن يكون فارغاً |
| `tax_rate`     | REAL         | نسبة الضريبة المطبقة على هذا الصنف (مثال: 0.15 لـ 15%) | القيمة الافتراضية 0.0 |

#### 2. جدول `Invoices` (الفواتير)
يحتوي هذا الجدول على معلومات عامة عن كل فاتورة عرض سعر.

| اسم الحقل      | نوع البيانات | الوصف                                   | ملاحظات        |
| :-------------- | :----------- | :-------------------------------------- | :-------------- |
| `invoice_id`    | INTEGER      | معرف فريد للفاتورة                     | مفتاح أساسي، ترقيم تلقائي |
| `invoice_date`  | TEXT         | تاريخ إنشاء الفاتورة (بصيغة YYYY-MM-DD HH:MM:SS) | لا يمكن أن يكون فارغاً |
| `customer_name` | TEXT         | اسم العميل (اختياري)                   |                 |
| `total_amount`  | REAL         | الإجمالي الكلي للفاتورة بعد الضرائب    |                 |
| `total_tax`     | REAL         | إجمالي مبلغ الضريبة في الفاتورة        |                 |

#### 3. جدول `InvoiceItems` (أصناف الفاتورة)
يربط هذا الجدول الأصناف بالفواتير ويحتوي على تفاصيل كل صنف داخل فاتورة معينة.

| اسم الحقل     | نوع البيانات | الوصف                                   | ملاحظات        |
| :------------- | :----------- | :-------------------------------------- | :-------------- |
| `invoice_item_id` | INTEGER      | معرف فريد لربط الصنف بالفاتورة         | مفتاح أساسي، ترقيم تلقائي |
| `invoice_id`   | INTEGER      | معرف الفاتورة التي ينتمي إليها الصنف   | مفتاح خارجي يشير إلى `Invoices.invoice_id` |
| `item_id`      | INTEGER      | معرف الصنف الذي تم تضمينه في الفاتورة | مفتاح خارجي يشير إلى `Items.item_id` |
| `quantity`     | INTEGER      | الكمية من هذا الصنف في الفاتورة        | لا يمكن أن يكون فارغاً، أكبر من 0 |
| `unit_price`   | REAL         | سعر الوحدة للصنف وقت إنشاء الفاتورة   | هذا مهم لتجنب تغير الأسعار مستقبلاً |
| `item_tax_rate`| REAL         | نسبة الضريبة المطبقة على هذا الصنف وقت إنشاء الفاتورة | هذا مهم لتجنب تغير نسب الضرائب مستقبلاً |
| `subtotal`     | REAL         | الإجمالي الفرعي لهذا الصنف (السعر * الكمية) |                 |
| `tax_amount`   | REAL         | مبلغ الضريبة لهذا الصنف                |                 |

### العلاقات بين الجداول:
*   **`Invoices`** و **`InvoiceItems`**: علاقة واحد إلى متعدد (One-to-Many).
    *   كل فاتورة (`Invoices.invoice_id`) يمكن أن تحتوي على عدة أصناف (`InvoiceItems.invoice_id`).
*   **`Items`** و **`InvoiceItems`**: علاقة واحد إلى متعدد (One-to-Many).
    *   كل صنف (`Items.item_id`) يمكن أن يظهر في عدة سجلات في جدول `InvoiceItems` (أي في فواتير مختلفة).

هذا التصميم يضمن المرونة في إضافة الأصناف وتطبيق الضرائب وحساب الإجماليات بشكل دقيق.




## تصميم مكونات البرنامج والكلاسات

سنقوم بتصميم البرنامج باستخدام مبدأ البرمجة كائنية التوجه (OOP) في بايثون، مما يسهل تنظيم الكود وإعادة استخدامه.

### الكلاسات الرئيسية المقترحة:

#### 1. `DatabaseManager` (مدير قاعدة البيانات)
مسؤول عن جميع التفاعلات مع قاعدة البيانات (SQLite).

*   **الخصائص:**
    *   `db_name`: اسم ملف قاعدة البيانات.
*   **الوظائف (Methods):**
    *   `__init__(self, db_name)`: تهيئة الاتصال بقاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة.
    *   `_execute_query(self, query, params=())`: دالة داخلية لتنفيذ استعلامات SQL (INSERT, UPDATE, DELETE).
    *   `_fetch_query(self, query, params=())`: دالة داخلية لتنفيذ استعلامات SQL وجلب البيانات (SELECT).
    *   `add_item(self, name, description, price, barcode, tax_rate)`: إضافة صنف جديد إلى جدول `Items`.
    *   `update_item(self, item_id, name=None, description=None, price=None, barcode=None, tax_rate=None)`: تحديث معلومات صنف موجود.
    *   `delete_item(self, item_id)`: حذف صنف من جدول `Items`.
    *   `get_item_by_id(self, item_id)`: جلب صنف بواسطة معرفه.
    *   `get_item_by_barcode(self, barcode)`: جلب صنف بواسطة الباركود الخاص به.
    *   `get_all_items(self)`: جلب جميع الأصناف.
    *   `create_invoice(self, customer_name)`: إنشاء فاتورة جديدة في جدول `Invoices` وإرجاع `invoice_id`.
    *   `add_invoice_item(self, invoice_id, item_id, quantity, unit_price, item_tax_rate, subtotal, tax_amount)`: إضافة صنف إلى فاتورة في جدول `InvoiceItems`.
    *   `get_invoice_details(self, invoice_id)`: جلب تفاصيل فاتورة معينة (معلومات الفاتورة والأصناف بداخلها).
    *   `update_invoice_totals(self, invoice_id, total_amount, total_tax)`: تحديث إجماليات الفاتورة في جدول `Invoices`.
    *   `get_all_invoices(self)`: جلب جميع الفواتير.

#### 2. `Item` (صنف)
يمثل كائن صنف فردي.

*   **الخصائص:**
    *   `item_id`, `name`, `description`, `price`, `barcode`, `tax_rate` (تتوافق مع حقول جدول `Items`).
*   **الوظائف (Methods):**
    *   `__init__(self, item_id, name, description, price, barcode, tax_rate)`: تهيئة كائن الصنف.
    *   `calculate_tax(self, quantity)`: حساب مبلغ الضريبة لكمية معينة من الصنف.
    *   `calculate_subtotal(self, quantity)`: حساب الإجمالي الفرعي لكمية معينة من الصنف.

#### 3. `InvoiceItem` (صنف الفاتورة)
يمثل كائن صنف داخل فاتورة معينة.

*   **الخصائص:**
    *   `invoice_item_id`, `invoice_id`, `item_id`, `quantity`, `unit_price`, `item_tax_rate`, `subtotal`, `tax_amount` (تتوافق مع حقول جدول `InvoiceItems`).
*   **الوظائف (Methods):**
    *   `__init__(self, invoice_item_id, invoice_id, item_id, quantity, unit_price, item_tax_rate, subtotal, tax_amount)`: تهيئة كائن صنف الفاتورة.

#### 4. `Invoice` (فاتورة)
يمثل كائن فاتورة عرض سعر.

*   **الخصائص:**
    *   `invoice_id`, `invoice_date`, `customer_name`, `total_amount`, `total_tax` (تتوافق مع حقول جدول `Invoices`).
    *   `items`: قائمة بكائنات `InvoiceItem` المرتبطة بهذه الفاتورة.
*   **الوظائف (Methods):**
    *   `__init__(self, invoice_id, invoice_date, customer_name, total_amount=0.0, total_tax=0.0)`: تهيئة كائن الفاتورة.
    *   `add_item(self, item: Item, quantity: int)`: إضافة صنف إلى الفاتورة (يجب أن يقوم بحساب `subtotal` و `tax_amount` وإنشاء كائن `InvoiceItem`).
    *   `calculate_totals(self)`: حساب `total_amount` و `total_tax` للفاتورة بناءً على الأصناف المضافة.
    *   `save_to_db(self, db_manager: DatabaseManager)`: حفظ الفاتورة وتفاصيل أصنافها في قاعدة البيانات.

#### 5. `Application` (التطبيق الرئيسي)
الكلاس الرئيسي الذي يربط جميع المكونات معًا ويدير تدفق البرنامج.

*   **الخصائص:**
    *   `db_manager`: كائن من `DatabaseManager`.
*   **الوظائف (Methods):**
    *   `__init__(self, db_name)`: تهيئة مدير قاعدة البيانات.
    *   `run(self)`: بدء تشغيل واجهة المستخدم الرئيسية (يمكن أن تكون حلقة تكرارية لتقديم الخيارات للمستخدم).
    *   `manage_items_menu(self)`: قائمة لإدارة الأصناف (إضافة، تعديل، حذف، عرض).
    *   `create_new_invoice_menu(self)`: قائمة لإنشاء فاتورة جديدة.
    *   `add_item_to_invoice_by_barcode(self, current_invoice: Invoice)`: إضافة صنف إلى الفاتورة الحالية باستخدام الباركود.
    *   `add_item_to_invoice_manually(self, current_invoice: Invoice)`: إضافة صنف إلى الفاتورة الحالية يدوياً.
    *   `view_invoices_menu(self)`: قائمة لعرض الفواتير المحفوظة.

### واجهات التعامل (اختياري):
*   **واجهة سطر الأوامر (CLI):** يمكن بناء واجهة بسيطة تعتمد على إدخال الأوامر من المستخدم وعرض النتائج في الطرفية.
*   **واجهة رسومية (GUI):** يمكن استخدام مكتبات مثل `Tkinter` أو `PyQt` أو `Kivy` لبناء واجهة رسومية أكثر تفاعلية في المستقبل.

هذا التصميم يوفر هيكلاً قوياً ومرناً لتطوير البرنامج.

### واجهات التعامل (توضيح):
بالنسبة لهذه المرحلة من المخطط، سنفترض واجهة سطر أوامر (CLI) بسيطة للتفاعل مع المستخدم. يمكن تطوير واجهة رسومية (GUI) في مراحل لاحقة.

**مثال على تدفق واجهة سطر الأوامر:**

```
مرحباً بك في برنامج فاتورة عرض السعر!

الرجاء اختيار أحد الخيارات التالية:
1. إدارة الأصناف
2. إنشاء فاتورة جديدة
3. عرض الفواتير المحفوظة
4. خروج

أدخل اختيارك: _
```

**تدفق إدارة الأصناف:**

```
قائمة إدارة الأصناف:
1. إضافة صنف جديد
2. تعديل صنف موجود
3. حذف صنف
4. عرض جميع الأصناف
5. العودة للقائمة الرئيسية

أدخل اختيارك: _
```

**تدفق إنشاء فاتورة جديدة:**

```
إنشاء فاتورة عرض سعر جديدة:
أدخل اسم العميل (اختياري): _

الرجاء اختيار طريقة إضافة الأصناف:
1. إضافة صنف بالباركود
2. إضافة صنف يدوياً
3. إنهاء الفاتورة وحساب الإجمالي
4. إلغاء الفاتورة

أدخل اختيارك: _
```

هذه الواجهات ستكون بمثابة نقطة انطلاق لتطوير التفاعل الفعلي مع المستخدم.




## تصميم مكونات البرنامج والكلاسات

سنقوم بتصميم البرنامج باستخدام مبدأ البرمجة كائنية التوجه (OOP) في بايثون، مما يسهل تنظيم الكود وإعادة استخدامه.

### الكلاسات الرئيسية المقترحة:

#### 1. `DatabaseManager` (مدير قاعدة البيانات)
مسؤول عن جميع التفاعلات مع قاعدة البيانات (SQLite).

*   **الخصائص:**
    *   `db_name`: اسم ملف قاعدة البيانات.
*   **الوظائف (Methods):**
    *   `__init__(self, db_name)`: تهيئة الاتصال بقاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة.
    *   `_execute_query(self, query, params=())`: دالة داخلية لتنفيذ استعلامات SQL (INSERT, UPDATE, DELETE).
    *   `_fetch_query(self, query, params=())`: دالة داخلية لتنفيذ استعلامات SQL وجلب البيانات (SELECT).
    *   `add_item(self, name, description, price, barcode, tax_rate)`: إضافة صنف جديد إلى جدول `Items`.
    *   `update_item(self, item_id, name=None, description=None, price=None, barcode=None, tax_rate=None)`: تحديث معلومات صنف موجود.
    *   `delete_item(self, item_id)`: حذف صنف من جدول `Items`.
    *   `get_item_by_id(self, item_id)`: جلب صنف بواسطة معرفه.
    *   `get_item_by_barcode(self, barcode)`: جلب صنف بواسطة الباركود الخاص به.
    *   `get_all_items(self)`: جلب جميع الأصناف.
    *   `create_invoice(self, customer_name)`: إنشاء فاتورة جديدة في جدول `Invoices` وإرجاع `invoice_id`.
    *   `add_invoice_item(self, invoice_id, item_id, quantity, unit_price, item_tax_rate, subtotal, tax_amount)`: إضافة صنف إلى فاتورة في جدول `InvoiceItems`.
    *   `get_invoice_details(self, invoice_id)`: جلب تفاصيل فاتورة معينة (معلومات الفاتورة والأصناف بداخلها).
    *   `update_invoice_totals(self, invoice_id, total_amount, total_tax)`: تحديث إجماليات الفاتورة في جدول `Invoices`.
    *   `get_all_invoices(self)`: جلب جميع الفواتير.

#### 2. `Item` (صنف)
يمثل كائن صنف فردي.

*   **الخصائص:**
    *   `item_id`, `name`, `description`, `price`, `barcode`, `tax_rate` (تتوافق مع حقول جدول `Items`).
*   **الوظائف (Methods):**
    *   `__init__(self, item_id, name, description, price, barcode, tax_rate)`: تهيئة كائن الصنف.
    *   `calculate_tax(self, quantity)`: حساب مبلغ الضريبة لكمية معينة من الصنف.
    *   `calculate_subtotal(self, quantity)`: حساب الإجمالي الفرعي لكمية معينة من الصنف.

#### 3. `InvoiceItem` (صنف الفاتورة)
يمثل كائن صنف داخل فاتورة معينة.

*   **الخصائص:**
    *   `invoice_item_id`, `invoice_id`, `item_id`, `quantity`, `unit_price`, `item_tax_rate`, `subtotal`, `tax_amount` (تتوافق مع حقول جدول `InvoiceItems`).
*   **الوظائف (Methods):**
    *   `__init__(self, invoice_item_id, invoice_id, item_id, quantity, unit_price, item_tax_rate, subtotal, tax_amount)`: تهيئة كائن صنف الفاتورة.

#### 4. `Invoice` (فاتورة)
يمثل كائن فاتورة عرض سعر.

*   **الخصائص:**
    *   `invoice_id`, `invoice_date`, `customer_name`, `total_amount`, `total_tax` (تتوافق مع حقول جدول `Invoices`).
    *   `items`: قائمة بكائنات `InvoiceItem` المرتبطة بهذه الفاتورة.
*   **الوظائف (Methods):**
    *   `__init__(self, invoice_id, invoice_date, customer_name, total_amount=0.0, total_tax=0.0)`: تهيئة كائن الفاتورة.
    *   `add_item(self, item: Item, quantity: int)`: إضافة صنف إلى الفاتورة (يجب أن يقوم بحساب `subtotal` و `tax_amount` وإنشاء كائن `InvoiceItem`).
    *   `calculate_totals(self)`: حساب `total_amount` و `total_tax` للفاتورة بناءً على الأصناف المضافة.
    *   `save_to_db(self, db_manager: DatabaseManager)`: حفظ الفاتورة وتفاصيل أصنافها في قاعدة البيانات.

#### 5. `Application` (التطبيق الرئيسي)
الكلاس الرئيسي الذي يربط جميع المكونات معًا ويدير تدفق البرنامج.

*   **الخصائص:**
    *   `db_manager`: كائن من `DatabaseManager`.
*   **الوظائف (Methods):**
    *   `__init__(self, db_name)`: تهيئة مدير قاعدة البيانات.
    *   `run(self)`: بدء تشغيل واجهة المستخدم الرئيسية (يمكن أن تكون حلقة تكرارية لتقديم الخيارات للمستخدم).
    *   `manage_items_menu(self)`: قائمة لإدارة الأصناف (إضافة، تعديل، حذف، عرض).
    *   `create_new_invoice_menu(self)`: قائمة لإنشاء فاتورة جديدة.
    *   `add_item_to_invoice_by_barcode(self, current_invoice: Invoice)`: إضافة صنف إلى الفاتورة الحالية باستخدام الباركود.
    *   `add_item_to_invoice_manually(self, current_invoice: Invoice)`: إضافة صنف إلى الفاتورة الحالية يدوياً.
    *   `view_invoices_menu(self)`: قائمة لعرض الفواتير المحفوظة.

### واجهات التعامل (اختياري):
*   **واجهة سطر الأوامر (CLI):** يمكن بناء واجهة بسيطة تعتمد على إدخال الأوامر من المستخدم وعرض النتائج في الطرفية.
*   **واجهة رسومية (GUI):** يمكن استخدام مكتبات مثل `Tkinter` أو `PyQt` أو `Kivy` لبناء واجهة رسومية أكثر تفاعلية في المستقبل.

هذا التصميم يوفر هيكلاً قوياً ومرناً لتطوير البرنامج.

### واجهات التعامل (توضيح):
بالنسبة لهذه المرحلة من المخطط، سنفترض واجهة سطر أوامر (CLI) بسيطة للتفاعل مع المستخدم. يمكن تطوير واجهة رسومية (GUI) في مراحل لاحقة.

**مثال على تدفق واجهة سطر الأوامر:**

```
مرحباً بك في برنامج فاتورة عرض السعر!

الرجاء اختيار أحد الخيارات التالية:
1. إدارة الأصناف
2. إنشاء فاتورة جديدة
3. عرض الفواتير المحفوظة
4. خروج

أدخل اختيارك: _
```

**تدفق إدارة الأصناف:**

```
قائمة إدارة الأصناف:
1. إضافة صنف جديد
2. تعديل صنف موجود
3. حذف صنف
4. عرض جميع الأصناف
5. العودة للقائمة الرئيسية

أدخل اختيارك: _
```

**تدفق إنشاء فاتورة جديدة:**

```
إنشاء فاتورة عرض سعر جديدة:
أدخل اسم العميل (اختياري): _

الرجاء اختيار طريقة إضافة الأصناف:
1. إضافة صنف بالباركود
2. إضافة صنف يدوياً
3. إنهاء الفاتورة وحساب الإجمالي
4. إلغاء الفاتورة

أدخل اختيارك: _
```

هذه الواجهات ستكون بمثابة نقطة انطلاق لتطوير التفاعل الفعلي مع المستخدم.




## مقدمة

يهدف هذا المخطط إلى توفير رؤية شاملة لتصميم وتطوير برنامج فاتورة عرض سعر باستخدام لغة بايثون وقاعدة بيانات SQLite. يركز البرنامج على تلبية المتطلبات الأساسية للأعمال التجارية الصغيرة والمتوسطة، بما في ذلك إدارة الأصناف، دعم الباركود، حساب الضرائب، وإدارة الفواتير بشكل فعال.

تم تصميم هذا المخطط ليكون دليلاً للمطورين، موضحاً الهيكل العام للبرنامج، تصميم قاعدة البيانات، والكلاسات الرئيسية ووظائفها. يهدف إلى ضمان بناء نظام قوي، مرن، وسهل الصيانة.

## خاتمة

يقدم هذا المخطط التفصيلي أساساً متيناً لتطوير برنامج فاتورة عرض سعر شامل وفعال. من خلال الالتزام بالمبادئ الموضحة هنا، يمكن بناء نظام يلبي المتطلبات الحالية ويوفر المرونة للتوسع المستقبلي، مثل إضافة واجهة رسومية أو ميزات إضافية لإدارة العملاء والموردين. نأمل أن يكون هذا المخطط نقطة انطلاق قوية لمشروع ناجح.


