import tkinter as tk
from tkinter import ttk, messagebox
from database_manager import DatabaseManager
from main_gui import ModernStyle


class CustomersManagementWindow:
    """نافذة إدارة العملاء"""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        """تهيئة نافذة إدارة العملاء"""
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.tree = None
        self.selected_customer_id = None
        
        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.tax_number_var = tk.StringVar()
        self.search_var = tk.StringVar()
        
        self.create_window()
        self.load_customers()
    
    def create_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة العملاء")
        self.window.geometry("1200x700")
        self.window.configure(bg=ModernStyle.BG_COLOR)
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إدارة العملاء", 
                               font=ModernStyle.FONT_TITLE,
                               foreground=ModernStyle.PRIMARY_COLOR)
        title_label.pack(pady=(0, 20))
        
        # إنشاء الإطارات
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار النموذج (يسار)
        self.create_form_frame(content_frame)
        
        # إطار الجدول (يمين)
        self.create_table_frame(content_frame)
        
        # إطار الأزرار السفلي
        self.create_buttons_frame(main_frame)
    
    def create_form_frame(self, parent):
        """إنشاء إطار النموذج"""
        form_frame = ttk.LabelFrame(parent, text="بيانات العميل", padding=15)
        form_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # اسم العميل
        ttk.Label(form_frame, text="اسم العميل *:", font=ModernStyle.FONT_MEDIUM).pack(anchor=tk.E, pady=(0, 5))
        name_entry = ttk.Entry(form_frame, textvariable=self.name_var, font=ModernStyle.FONT_MEDIUM, width=30)
        name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # رقم الهاتف
        ttk.Label(form_frame, text="رقم الهاتف:", font=ModernStyle.FONT_MEDIUM).pack(anchor=tk.E, pady=(0, 5))
        phone_entry = ttk.Entry(form_frame, textvariable=self.phone_var, font=ModernStyle.FONT_MEDIUM, width=30)
        phone_entry.pack(fill=tk.X, pady=(0, 15))
        
        # البريد الإلكتروني
        ttk.Label(form_frame, text="البريد الإلكتروني:", font=ModernStyle.FONT_MEDIUM).pack(anchor=tk.E, pady=(0, 5))
        email_entry = ttk.Entry(form_frame, textvariable=self.email_var, font=ModernStyle.FONT_MEDIUM, width=30)
        email_entry.pack(fill=tk.X, pady=(0, 15))
        
        # العنوان
        ttk.Label(form_frame, text="العنوان:", font=ModernStyle.FONT_MEDIUM).pack(anchor=tk.E, pady=(0, 5))
        address_text = tk.Text(form_frame, height=3, width=30, font=ModernStyle.FONT_MEDIUM)
        address_text.pack(fill=tk.X, pady=(0, 15))
        
        # ربط النص بالمتغير
        def update_address(*args):
            self.address_var.set(address_text.get("1.0", tk.END).strip())
        
        address_text.bind('<KeyRelease>', update_address)
        
        # الرقم الضريبي
        ttk.Label(form_frame, text="الرقم الضريبي:", font=ModernStyle.FONT_MEDIUM).pack(anchor=tk.E, pady=(0, 5))
        tax_entry = ttk.Entry(form_frame, textvariable=self.tax_number_var, font=ModernStyle.FONT_MEDIUM, width=30)
        tax_entry.pack(fill=tk.X, pady=(0, 15))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(form_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # زر إضافة
        add_btn = ttk.Button(buttons_frame, text="إضافة عميل", command=self.add_customer)
        add_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر تحديث
        update_btn = ttk.Button(buttons_frame, text="تحديث العميل", command=self.update_customer)
        update_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر حذف
        delete_btn = ttk.Button(buttons_frame, text="حذف العميل", command=self.delete_customer)
        delete_btn.pack(fill=tk.X, pady=(0, 10))
        
        # زر مسح النموذج
        clear_btn = ttk.Button(buttons_frame, text="مسح النموذج", command=self.clear_form)
        clear_btn.pack(fill=tk.X)
        
        # حفظ مرجع للنص
        self.address_text = address_text
    
    def create_table_frame(self, parent):
        """إنشاء إطار الجدول"""
        table_frame = ttk.LabelFrame(parent, text="قائمة العملاء", padding=10)
        table_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # إطار البحث
        search_frame = ttk.Frame(table_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="البحث:", font=ModernStyle.FONT_MEDIUM).pack(side=tk.RIGHT, padx=(10, 0))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, font=ModernStyle.FONT_MEDIUM)
        search_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # إنشاء الجدول
        columns = ("ID", "الاسم", "الهاتف", "البريد الإلكتروني", "الرقم الضريبي")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        self.tree.heading("ID", text="المعرف")
        self.tree.heading("الاسم", text="اسم العميل")
        self.tree.heading("الهاتف", text="رقم الهاتف")
        self.tree.heading("البريد الإلكتروني", text="البريد الإلكتروني")
        self.tree.heading("الرقم الضريبي", text="الرقم الضريبي")
        
        # تعريف عرض الأعمدة
        self.tree.column("ID", width=80, anchor=tk.CENTER)
        self.tree.column("الاسم", width=200, anchor=tk.E)
        self.tree.column("الهاتف", width=150, anchor=tk.CENTER)
        self.tree.column("البريد الإلكتروني", width=200, anchor=tk.CENTER)
        self.tree.column("الرقم الضريبي", width=150, anchor=tk.CENTER)
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر على الجدول
        self.tree.bind('<<TreeviewSelect>>', self.on_customer_select)
        
        # قائمة السياق (النقر بالزر الأيمن)
        self.create_context_menu()
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="تحديث", command=self.update_customer)
        self.context_menu.add_command(label="حذف", command=self.delete_customer)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="عرض الفواتير", command=self.view_customer_invoices)
        
        # ربط القائمة بالجدول
        self.tree.bind("<Button-3>", self.show_context_menu)
    
    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار السفلي"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # زر إغلاق
        close_btn = ttk.Button(buttons_frame, text="إغلاق", command=self.close_window)
        close_btn.pack(side=tk.LEFT)
        
        # زر تحديث القائمة
        refresh_btn = ttk.Button(buttons_frame, text="تحديث القائمة", command=self.load_customers)
        refresh_btn.pack(side=tk.LEFT, padx=(10, 0))
    
    def validate_form(self) -> bool:
        """التحقق من صحة النموذج"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return False
        
        # التحقق من صحة البريد الإلكتروني إذا تم إدخاله
        email = self.email_var.get().strip()
        if email and "@" not in email:
            messagebox.showerror("خطأ", "يرجى إدخال بريد إلكتروني صحيح")
            return False
        
        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_var.set("")
        self.phone_var.set("")
        self.email_var.set("")
        self.address_var.set("")
        self.tax_number_var.set("")
        self.address_text.delete("1.0", tk.END)
        self.selected_customer_id = None
    
    def load_customers(self):
        """تحميل قائمة العملاء"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # جلب العملاء
        customers = self.db_manager.get_all_customers()
        
        # إضافة العملاء للجدول
        for customer in customers:
            self.tree.insert("", tk.END, values=(
                customer['customer_id'],
                customer['name'],
                customer['phone'] or "",
                customer['email'] or "",
                customer['tax_number'] or ""
            ))
    
    def on_search(self, event=None):
        """البحث في العملاء"""
        search_term = self.search_var.get().strip()
        
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # جلب النتائج
        if search_term:
            customers = self.db_manager.search_customers(search_term)
        else:
            customers = self.db_manager.get_all_customers()
        
        # إضافة النتائج للجدول
        for customer in customers:
            self.tree.insert("", tk.END, values=(
                customer['customer_id'],
                customer['name'],
                customer['phone'] or "",
                customer['email'] or "",
                customer['tax_number'] or ""
            ))
    
    def on_customer_select(self, event):
        """عند اختيار عميل من الجدول"""
        selection = self.tree.selection()
        if not selection:
            return
        
        # جلب بيانات العميل المحدد
        item = self.tree.item(selection[0])
        customer_id = item['values'][0]
        
        # جلب تفاصيل العميل من قاعدة البيانات
        customer = self.db_manager.get_customer_by_id(customer_id)
        if customer:
            self.selected_customer_id = customer_id
            self.name_var.set(customer['name'])
            self.phone_var.set(customer['phone'] or "")
            self.email_var.set(customer['email'] or "")
            self.address_var.set(customer['address'] or "")
            self.tax_number_var.set(customer['tax_number'] or "")
            
            # تحديث النص
            self.address_text.delete("1.0", tk.END)
            self.address_text.insert("1.0", customer['address'] or "")
    
    def add_customer(self):
        """إضافة عميل جديد"""
        if not self.validate_form():
            return
        
        # إضافة العميل
        success = self.db_manager.add_customer(
            name=self.name_var.get().strip(),
            phone=self.phone_var.get().strip(),
            email=self.email_var.get().strip(),
            address=self.address_var.get().strip(),
            tax_number=self.tax_number_var.get().strip()
        )
        
        if success:
            messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            self.clear_form()
            self.load_customers()
        else:
            messagebox.showerror("خطأ", "فشل في إضافة العميل")
    
    def update_customer(self):
        """تحديث عميل موجود"""
        if not self.selected_customer_id:
            messagebox.showerror("خطأ", "يرجى اختيار عميل للتحديث")
            return
        
        if not self.validate_form():
            return
        
        # تحديث العميل
        success = self.db_manager.update_customer(
            customer_id=self.selected_customer_id,
            name=self.name_var.get().strip(),
            phone=self.phone_var.get().strip(),
            email=self.email_var.get().strip(),
            address=self.address_var.get().strip(),
            tax_number=self.tax_number_var.get().strip()
        )
        
        if success:
            messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
            self.load_customers()
        else:
            messagebox.showerror("خطأ", "فشل في تحديث العميل")
    
    def delete_customer(self):
        """حذف عميل"""
        if not self.selected_customer_id:
            messagebox.showerror("خطأ", "يرجى اختيار عميل للحذف")
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", 
                                   "هل أنت متأكد من حذف هذا العميل؟\n"
                                   "سيتم الاحتفاظ بالفواتير المرتبطة به.")
        
        if result:
            success = self.db_manager.delete_customer(self.selected_customer_id)
            if success:
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.clear_form()
                self.load_customers()
            else:
                messagebox.showerror("خطأ", "فشل في حذف العميل")
    
    def view_customer_invoices(self):
        """عرض فواتير العميل"""
        if not self.selected_customer_id:
            messagebox.showerror("خطأ", "يرجى اختيار عميل")
            return
        
        # هذه الوظيفة يمكن تطويرها لاحقاً لعرض فواتير العميل
        messagebox.showinfo("قريباً", "ستتوفر هذه الميزة قريباً")
    
    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()
