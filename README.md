# نظام إدارة الفواتير وعروض الأسعار
## Invoice and Quotation Management System

تطبيق سطح مكتب شامل لإدارة الفواتير وعروض الأسعار مع دعم الباركود والضرائب والطباعة.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![Tkinter](https://img.shields.io/badge/GUI-Tkinter-green.svg)
![SQLite](https://img.shields.io/badge/Database-SQLite-orange.svg)
![License](https://img.shields.io/badge/License-MIT-red.svg)

## 🌟 المميزات الرئيسية

### 📦 إدارة الأصناف
- إضافة وتعديل وحذف الأصناف
- دعم الباركود لكل صنف
- تحديد معدل الضريبة لكل صنف
- البحث والتصفية المتقدمة

### 🧾 إدارة الفواتير
- إنشاء فواتير جديدة بسهولة
- إضافة أصناف متعددة للفاتورة الواحدة
- حساب الضرائب والإجماليات تلقائياً
- دعم البحث بالباركود
- حفظ وعرض الفواتير المحفوظة

### 🖨️ الطباعة والتصدير
- معاينة الطباعة قبل الطباعة الفعلية
- تصدير الفواتير (قابل للتطوير لدعم PDF)
- تخطيط احترافي للفواتير

### 📊 الإحصائيات والتقارير
- إحصائيات المبيعات
- إجمالي الضرائب
- عدد الفواتير والأصناف

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- Windows 10/11 أو Linux أو macOS
- Python 3.8 أو أحدث
- ذاكرة RAM: 512 MB كحد أدنى
- مساحة القرص: 100 MB

### المكتبات المطلوبة
```bash
# المكتبات الأساسية (مدمجة مع Python)
tkinter
sqlite3
datetime
typing

# المكتبات الإضافية (اختيارية)
Pillow  # لدعم الصور المحسن
```

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/invoice-management-system.git
cd invoice-management-system
```

### 2. تثبيت المتطلبات (اختياري)
```bash
pip install Pillow
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## 📁 هيكل المشروع

```
invoice-management-system/
│
├── main.py                 # الملف الرئيسي لتشغيل التطبيق
├── database_manager.py     # إدارة قاعدة البيانات
├── models.py              # نماذج البيانات (Item, Invoice, InvoiceItem)
├── main_gui.py            # النافذة الرئيسية والأنماط
├── items_management.py    # نافذة إدارة الأصناف
├── invoice_creation.py    # نافذة إنشاء الفواتير
├── invoices_view.py       # نافذة عرض الفواتير
├── invoice_system.db      # قاعدة البيانات (تُنشأ تلقائياً)
└── README.md             # هذا الملف
```

## 🎯 كيفية الاستخدام

### البدء السريع
1. شغل التطبيق باستخدام `python main.py`
2. ستظهر النافذة الرئيسية مع الأزرار الرئيسية
3. ابدأ بإضافة الأصناف من خلال "إدارة الأصناف"
4. أنشئ فاتورة جديدة من خلال "إنشاء فاتورة جديدة"

### إدارة الأصناف
1. اضغط على "إدارة الأصناف"
2. اضغط "إضافة صنف جديد"
3. املأ البيانات المطلوبة (الاسم، الوصف، السعر، الباركود، معدل الضريبة)
4. اضغط "حفظ"

### إنشاء فاتورة
1. اضغط على "إنشاء فاتورة جديدة"
2. أدخل اسم العميل
3. أضف الأصناف بإحدى الطرق:
   - إدخال الباركود مباشرة
   - اختيار من القائمة المنسدلة
4. حدد الكمية واضغط "إضافة للفاتورة"
5. كرر العملية لإضافة أصناف أخرى
6. اضغط "حفظ الفاتورة" عند الانتهاء

### عرض الفواتير
1. اضغط على "عرض الفواتير"
2. اختر فاتورة من القائمة
3. ستظهر تفاصيل الفاتورة على الجانب الأيمن
4. يمكنك طباعة أو تصدير الفاتورة

## 🔧 الإعدادات والتخصيص

### تخصيص الألوان
يمكن تعديل الألوان في ملف `main_gui.py` في كلاس `ModernStyle`:

```python
class ModernStyle:
    PRIMARY_COLOR = "#2E86AB"      # اللون الأساسي
    SECONDARY_COLOR = "#A23B72"    # اللون الثانوي
    ACCENT_COLOR = "#F18F01"       # لون التمييز
    # ... باقي الألوان
```

### تخصيص معدل الضريبة الافتراضي
يمكن تعديل معدل الضريبة الافتراضي في ملف `models.py`:

```python
class Item:
    def __init__(self, ..., tax_rate: float = 15.0):  # 15% افتراضي
```

## 🗄️ قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite مع الجداول التالية:

### جدول Items (الأصناف)
- `item_id`: المعرف الفريد
- `name`: اسم الصنف
- `description`: وصف الصنف
- `price`: السعر
- `barcode`: الباركود
- `tax_rate`: معدل الضريبة
- `created_at`: تاريخ الإنشاء

### جدول Invoices (الفواتير)
- `invoice_id`: رقم الفاتورة
- `customer_name`: اسم العميل
- `invoice_date`: تاريخ الفاتورة
- `total_amount`: الإجمالي الكلي
- `total_tax`: إجمالي الضريبة

### جدول InvoiceItems (أصناف الفاتورة)
- `invoice_item_id`: المعرف الفريد
- `invoice_id`: رقم الفاتورة
- `item_id`: معرف الصنف
- `quantity`: الكمية
- `unit_price`: سعر الوحدة
- `item_tax_rate`: معدل ضريبة الصنف
- `subtotal`: الإجمالي الفرعي
- `tax_amount`: مبلغ الضريبة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في استيراد المكتبات
```bash
ModuleNotFoundError: No module named 'tkinter'
```
**الحل**: تأكد من تثبيت Python مع دعم tkinter

#### خطأ في قاعدة البيانات
```bash
sqlite3.OperationalError: database is locked
```
**الحل**: تأكد من إغلاق جميع نسخ التطبيق قبل إعادة التشغيل

#### مشاكل في عرض النصوص العربية
**الحل**: تأكد من أن النظام يدعم الخطوط العربية

## 🔮 التطوير المستقبلي

### ميزات مخططة
- [ ] تصدير PDF فعلي
- [ ] دعم العملات المتعددة
- [ ] نظام المستخدمين والصلاحيات
- [ ] تقارير مفصلة ورسوم بيانية
- [ ] دعم الطباعة المباشرة للطابعات الحرارية
- [ ] واجهة ويب اختيارية
- [ ] دعم قواعد البيانات الخارجية (MySQL, PostgreSQL)
- [ ] نظام النسخ الاحتياطي التلقائي

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. تنفيذ التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. رفع التغييرات (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://example.com
- **التوثيق**: https://docs.example.com

## 🙏 شكر وتقدير

- شكر خاص لمجتمع Python
- مكتبة Tkinter للواجهات الرسومية
- قاعدة بيانات SQLite

---

**ملاحظة**: هذا التطبيق تم تطويره لأغراض تعليمية وتجارية. يرجى التأكد من مطابقته لمتطلبات بلدك القانونية قبل الاستخدام التجاري.
