import tkinter as tk
from tkinter import ttk, messagebox, font
import os
from PIL import Image, ImageTk
from database_manager import DatabaseManager
from models import Item, Invoice, InvoiceItem


class ModernStyle:
    """كلاس لتعريف الألوان والأنماط العصرية"""
    
    # الألوان الأساسية
    PRIMARY_COLOR = "#2E86AB"      # أزرق أساسي
    SECONDARY_COLOR = "#A23B72"    # وردي ثانوي
    ACCENT_COLOR = "#F18F01"       # برتقالي للتمييز
    SUCCESS_COLOR = "#4CAF50"      # أخضر للنجاح
    WARNING_COLOR = "#FF9800"      # برتقالي للتحذير
    ERROR_COLOR = "#F44336"        # أحمر للأخطاء
    DANGER_COLOR = "#F44336"       # أحمر للخطر
    
    # ألوان الخلفية
    BG_COLOR = "#F5F7FA"          # خلفية فاتحة
    CARD_BG = "#FFFFFF"           # خلفية البطاقات
    SIDEBAR_BG = "#2C3E50"        # خلفية الشريط الجانبي
    
    # ألوان النص
    TEXT_PRIMARY = "#2C3E50"      # نص أساسي
    TEXT_SECONDARY = "#7F8C8D"    # نص ثانوي
    TEXT_WHITE = "#FFFFFF"        # نص أبيض
    
    # ألوان الحدود
    BORDER_COLOR = "#E1E8ED"      # حدود فاتحة
    BORDER_FOCUS = "#3498DB"      # حدود عند التركيز
    
    # أحجام الخطوط
    FONT_LARGE = ("Segoe UI", 16, "bold")
    FONT_MEDIUM = ("Segoe UI", 12)
    FONT_SMALL = ("Segoe UI", 10)
    FONT_TITLE = ("Segoe UI", 20, "bold")


class MainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, db_manager=None):
        """تهيئة النافذة الرئيسية"""
        self.root = tk.Tk()
        self.db_manager = db_manager or DatabaseManager()
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("نظام فواتير عرض الأسعار")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        self.root.configure(bg=ModernStyle.BG_COLOR)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """إعداد أنماط ttk"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # أنماط الأزرار
        style.configure("Primary.TButton",
                       background=ModernStyle.PRIMARY_COLOR,
                       foreground=ModernStyle.TEXT_WHITE,
                       font=ModernStyle.FONT_MEDIUM,
                       padding=(20, 10))
        
        style.configure("Secondary.TButton",
                       background=ModernStyle.SECONDARY_COLOR,
                       foreground=ModernStyle.TEXT_WHITE,
                       font=ModernStyle.FONT_MEDIUM,
                       padding=(15, 8))
        
        style.configure("Accent.TButton",
                       background=ModernStyle.ACCENT_COLOR,
                       foreground=ModernStyle.TEXT_WHITE,
                       font=ModernStyle.FONT_MEDIUM,
                       padding=(15, 8))
        
        # أنماط التسميات
        style.configure("Title.TLabel",
                       background=ModernStyle.BG_COLOR,
                       foreground=ModernStyle.TEXT_PRIMARY,
                       font=ModernStyle.FONT_TITLE)
        
        style.configure("Heading.TLabel",
                       background=ModernStyle.BG_COLOR,
                       foreground=ModernStyle.TEXT_PRIMARY,
                       font=ModernStyle.FONT_LARGE)
        
        # أنماط الإطارات
        style.configure("Card.TFrame",
                       background=ModernStyle.CARD_BG,
                       relief="flat",
                       borderwidth=1)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, style="Card.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 30))
        
        title_label = ttk.Label(title_frame, 
                               text="🧾 نظام فواتير عرض الأسعار",
                               style="Title.TLabel")
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame,
                                  text="إدارة شاملة للأصناف والفواتير",
                                  font=ModernStyle.FONT_MEDIUM,
                                  foreground=ModernStyle.TEXT_SECONDARY,
                                  background=ModernStyle.BG_COLOR)
        subtitle_label.pack(pady=(5, 0))
        
        # إطار الأزرار الرئيسية
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(expand=True)
        
        # إنشاء الأزرار في شبكة
        self.create_main_buttons(buttons_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        # إطار الشبكة
        grid_frame = ttk.Frame(parent)
        grid_frame.pack(expand=True)
        
        # تكوين الشبكة
        for i in range(3):
            grid_frame.grid_rowconfigure(i, weight=1)
        for j in range(2):
            grid_frame.grid_columnconfigure(j, weight=1)

        # زر إدارة الأصناف
        items_btn = ttk.Button(grid_frame,
                              text="📦 إدارة الأصناف\nإضافة وتعديل الأصناف",
                              style="Primary.TButton",
                              command=self.open_items_management)
        items_btn.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")

        # زر إدارة العملاء
        customers_btn = ttk.Button(grid_frame,
                                 text="👥 إدارة العملاء\nإضافة وتعديل العملاء",
                                 style="Success.TButton",
                                 command=self.open_customers_management)
        customers_btn.grid(row=0, column=1, padx=20, pady=20, sticky="nsew")

        # زر إنشاء فاتورة جديدة
        new_invoice_btn = ttk.Button(grid_frame,
                                   text="📄 فاتورة جديدة\nإنشاء فاتورة عرض سعر",
                                   style="Secondary.TButton",
                                   command=self.open_new_invoice)
        new_invoice_btn.grid(row=1, column=0, padx=20, pady=20, sticky="nsew")

        # زر عرض الفواتير
        view_invoices_btn = ttk.Button(grid_frame,
                                     text="📋 الفواتير المحفوظة\nعرض وطباعة الفواتير",
                                     style="Accent.TButton",
                                     command=self.open_invoices_view)
        view_invoices_btn.grid(row=1, column=1, padx=20, pady=20, sticky="nsew")

        # زر الإحصائيات
        stats_btn = ttk.Button(grid_frame,
                             text="📊 الإحصائيات\nتقارير المبيعات والأرباح",
                             style="Primary.TButton",
                             command=self.show_statistics)
        stats_btn.grid(row=2, column=0, columnspan=2, padx=20, pady=20, sticky="nsew")

        # تحديد الحد الأدنى لحجم الأزرار
        for i in range(3):
            grid_frame.grid_rowconfigure(i, minsize=120)
        for j in range(2):
            grid_frame.grid_columnconfigure(j, minsize=250)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(30, 0))
        
        # خط فاصل
        separator = ttk.Separator(status_frame, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=(0, 10))
        
        # معلومات النظام
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X)
        
        # عدد الأصناف
        items_count = len(self.db_manager.get_all_items())
        items_label = ttk.Label(info_frame,
                               text=f"الأصناف: {items_count}",
                               font=ModernStyle.FONT_SMALL,
                               foreground=ModernStyle.TEXT_SECONDARY,
                               background=ModernStyle.BG_COLOR)
        items_label.pack(side=tk.LEFT)
        
        # عدد الفواتير
        invoices_count = len(self.db_manager.get_all_invoices())
        invoices_label = ttk.Label(info_frame,
                                  text=f"الفواتير: {invoices_count}",
                                  font=ModernStyle.FONT_SMALL,
                                  foreground=ModernStyle.TEXT_SECONDARY,
                                  background=ModernStyle.BG_COLOR)
        invoices_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # معلومات النسخة
        version_label = ttk.Label(info_frame,
                                 text="الإصدار 1.0",
                                 font=ModernStyle.FONT_SMALL,
                                 foreground=ModernStyle.TEXT_SECONDARY,
                                 background=ModernStyle.BG_COLOR)
        version_label.pack(side=tk.RIGHT)
    
    def open_items_management(self):
        """فتح نافذة إدارة الأصناف"""
        from items_management import ItemsManagementWindow
        ItemsManagementWindow(self.root, self.db_manager)

    def open_customers_management(self):
        """فتح نافذة إدارة العملاء"""
        from customers_management import CustomersManagementWindow
        CustomersManagementWindow(self.root, self.db_manager)
    
    def open_new_invoice(self):
        """فتح نافذة إنشاء فاتورة جديدة"""
        from invoice_creation import InvoiceCreationWindow
        InvoiceCreationWindow(self.root, self.db_manager)
    
    def open_invoices_view(self):
        """فتح نافذة عرض الفواتير"""
        from invoices_view import InvoicesViewWindow
        InvoicesViewWindow(self.root, self.db_manager)
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        stats = self.db_manager.get_invoice_statistics()
        
        message = f"""📊 إحصائيات النظام:
        
🧾 عدد الفواتير: {stats['total_invoices']}
💰 إجمالي المبيعات: {stats['total_sales']:.2f} ريال
🏛️ إجمالي الضرائب: {stats['total_taxes']:.2f} ريال
📦 عدد الأصناف: {len(self.db_manager.get_all_items())}
        """
        
        messagebox.showinfo("إحصائيات النظام", message)
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
